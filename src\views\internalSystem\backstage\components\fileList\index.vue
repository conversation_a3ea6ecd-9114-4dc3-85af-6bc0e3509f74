<template>
  <div>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      append-to-body
      @close="dialogCancel"
      width="40%"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      v-dialogDrag
    >
      <div class="upload-section">
        <el-form :inline="true" class="mb20">
          <el-form-item label="附件类型" :required="true">
            <el-select v-model="selectedFileType" placeholder="请先选择附件类型再上传" style="width: 200px;">
              <el-option label="合同附件" value="contract"></el-option>
              <el-option label="发票附件" value="invoice"></el-option>
              <el-option label="其他附件" value="other"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-upload
              ref="uploadFile"
              :action="`${environment.internalSystemAPI}assets/uploadFile`"
              :on-error="recovery"
              :on-success="handlePreview"
              :auto-upload="true"
              :show-file-list="false"
              :before-upload="checkFile"
              :disabled="!selectedFileType"
            >
              <el-button size="small" @click="dialogCancel">取 消</el-button>
              <el-button size="small" type="primary" :disabled="!selectedFileType">上传</el-button>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <TableView
        class="mt20"
        :tableList="tableList"
        :tableData="tableData"
        :tableHeight="460"
        isDel="permanent_button"
        @del="del"
        isFive="permanent_button"
        :fiveTitle="'下载'"
        @five="downloadFile"
        isFour="permanent_button"
        :fourTitle="'发送邮件'"
        @four="openEmailDialogForFile"
        thridTitle="发送日志"
        isThrid="permanent_button"
        @thrid="showEmailLogs"
        :handleWidth="220"
      ></TableView>
      <Pagination ref="file_pagination" @success="getList" />
      <!-- <span slot="footer" class="dialog-footer">
      <el-button @click="dialogCancel">取 消</el-button>
    </span> -->

      <!-- 文件预览弹窗 -->
      <el-dialog
        :visible="showDoc == true || showPdf == true || showImages == true"
        :before-close="closePreviewClick"
        :width="'70%'"
        :height="'70%'"
        class="dialog-div-pre-style"
        :modal="false"
        center
        :title="anexoName"
      >
        <div v-if="showPdf == true">
          <el-button-group>
            <el-button
              type="primary"
              icon="el-icon-arrow-left"
              size="mini"
              @click="prePage"
              >上一页</el-button
            >
            <el-button type="primary" size="mini" @click="nextPage"
              >下一页<i class="el-icon-arrow-right el-icon--right"></i
            ></el-button>
          </el-button-group>
          <div style="margintop: 10px; color: #409eff">
            {{ pageNum }} / {{ pageTotalNum }}
          </div>
          <pdf
            ref="pdf"
            :src="url"
            :page="pageNum"
            @progress="loadedRatio = $event"
            @num-pages="pageTotalNum = $event"
          >
          </pdf>
        </div>

        <img v-else-if="showImages == true" :src="url" />
        <div
          v-else-if="showDoc == true"
          class="dialog-body-content-base-style"
          style="height: 600px"
        >
          <iframe
            :src="
              '//www.xdocin.com/xdoc?_func=to&_format=html&_cache=1&_xdoc=' +
                url
            "
            width="100%"
            height="100%"
            frameborder="0"
          >
          </iframe>
        </div>
      </el-dialog>
    </el-dialog>

    <!-- 邮件发送弹窗 -->
    <el-dialog
      title="发送邮件"
      :visible.sync="emailDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      append-to-body
    >
      <el-form :model="emailForm" ref="emailForm" label-width="100px">
        <el-form-item
          label="收件人邮箱"
          prop="email"
          :rules="[
            { required: true, message: '请输入收件人邮箱', trigger: 'blur' },
            { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
          ]"
        >
          <el-input
            v-model="emailForm.email"
            placeholder="请输入收件人邮箱地址"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="邮件主题"
          prop="subject"
          :rules="[{ required: true, message: '请输入邮件主题', trigger: 'blur' }]"
        >
          <el-input
            v-model="emailForm.subject"
            placeholder="请输入邮件主题"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="附件地址"
          prop="file_url"
          :rules="[{ required: true, message: '请输入附件地址', trigger: 'blur' }]"
        >
          <el-input
            v-model="emailForm.file_url"
            placeholder="请输入附件地址"
          ></el-input>
        </el-form-item>
        <el-form-item label="邮件内容">
          <el-input
            v-model="emailForm.content"
            type="textarea"
            rows="4"
            placeholder="请输入邮件内容（可选）"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="emailDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="sendEmail"
          :loading="emailSending"
          >发 送</el-button
        >
      </div>
    </el-dialog>
    
    <!-- 邮件发送日志弹窗 -->
    <EmailLogList ref="emailLogList" />
  </div>
</template>
<script src="./index.js">
</script>