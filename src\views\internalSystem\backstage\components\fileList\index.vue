<template>
  <div>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      append-to-body
      @close="dialogCancel"
      width="40%"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      v-dialogDrag
    >
      <div class="upload-section">
        <el-form :inline="true" class="mb20">
          <el-form-item label="附件类型" :required="true">
            <el-select v-model="selectedFileType" placeholder="请先选择附件类型再上传" style="width: 200px;">
              <el-option label="合同附件" value="contract"></el-option>
              <el-option label="发票附件" value="invoice"></el-option>
              <el-option label="其他附件" value="other"></el-option>
            </el-select>
            <div style="font-size: 12px; color: #909399; margin-top: 5px;" v-if="userInfo && userInfo.departmentName">
              已根据您的部门（{{ userInfo.departmentName }}）自动选择附件类型
            </div>
          </el-form-item>
          <el-form-item>
            <el-upload
              ref="uploadFile"
              :action="`${environment.internalSystemAPI}assets/uploadFile`"
              :on-error="recovery"
              :on-success="handlePreview"
              :auto-upload="true"
              :show-file-list="false"
              :before-upload="checkFile"
              :disabled="!selectedFileType"
            >
              <el-button size="small" @click="dialogCancel">取 消</el-button>
              <el-button size="small" type="primary" :disabled="!selectedFileType">上传</el-button>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <TableView
        class="mt20"
        :tableList="tableList"
        :tableData="tableData"
        :tableHeight="460"
        isFive="permanent_button"
        :fiveTitle="'预览'"
        @five="previewFile"
        isFour="permanent_button"
        :fourTitle="'下载'"
        @four="downloadFile"
        isDel="permanent_button"
        @del="del"
        :handleWidth="160"
      ></TableView>
      <Pagination ref="file_pagination" @success="getList" />
      <!-- <span slot="footer" class="dialog-footer">
      <el-button @click="dialogCancel">取 消</el-button>
    </span> -->

      <!-- 文件预览弹窗 -->
      <el-dialog
        :visible="showDoc == true || showPdf == true || showImages == true"
        :before-close="closePreviewClick"
        :width="'70%'"
        :height="'70%'"
        class="dialog-div-pre-style"
        :modal="false"
        center
        :title="anexoName"
      >
        <div v-if="showPdf == true">
          <el-button-group>
            <el-button
              type="primary"
              icon="el-icon-arrow-left"
              size="mini"
              @click="prePage"
              >上一页</el-button
            >
            <el-button type="primary" size="mini" @click="nextPage"
              >下一页<i class="el-icon-arrow-right el-icon--right"></i
            ></el-button>
          </el-button-group>
          <div style="margintop: 10px; color: #409eff">
            {{ pageNum }} / {{ pageTotalNum }}
          </div>
          <pdf
            ref="pdf"
            :src="url"
            :page="pageNum"
            @progress="loadedRatio = $event"
            @num-pages="pageTotalNum = $event"
          >
          </pdf>
        </div>

        <img v-else-if="showImages == true" :src="url" />
        <div
          v-else-if="showDoc == true"
          class="dialog-body-content-base-style"
          style="height: 600px"
        >
          <iframe
            :src="
              '//www.xdocin.com/xdoc?_func=to&_format=html&_cache=1&_xdoc=' +
                url
            "
            width="100%"
            height="100%"
            frameborder="0"
          >
          </iframe>
        </div>
      </el-dialog>
    </el-dialog>


  </div>
</template>
<script src="./index.js">
</script>