import API from "@/api/internalSystem/customerManage/implementation";
import API2 from "@/api/internalSystem/salesManage/contract";
import ContractList from "../contractList/index.vue";
import FileList from "@/views/internalSystem/backstage/components/fileList/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import { getOptions } from "@/common/internalSystem/common.js";
import { mapGetters } from "vuex";
export default {
  name: "addImplementation",
  components: {
    MyDate,
    ContractList,
    FileList,
    TableView
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        customer_contract_id: "",
        contract_no: "",
        customer_name: "",
        fk_sell_employee_id: "",
        fk_sell_employee_name: "",
        fk_customer_id: "",
        link_man: "",
        // address: "",
        phone: "",
        // customer_type: "",
        implementation_time: "",
        over_time: null,
        sell_type: null,
        train_type: "",
        implementation_conclusion: "",
        original_port_count: "",
        add_port_count: "",
        version_no: "",
        follow_records: "",
        remark: "",
        attention_matters: "",
      },
      implementationUserList:[],
      rules: {
        contract_no: [
          {
            required: true,
            message: " ",
          },
        ],
        customer_name: [
          {
            required: true,
            message: " ",
          },
        ],
  
        software_version: [
          {
            required: true,
            message: " ",
            trigger: "change",
          },
        ],
        link_man: [
          {
            required: true,
            message: " ",
          },
        ],
        phone: [
          {
            required: true,
            message: " ",
          },
        ],
        // address: [{
        //   required: true,
        //   message: "请输入详细地址"
        // }],
        // customer_type: [{
        //   required: true,
        //   message: "请选择客户类型"
        // }],
        implementation_time: [
          {
            required: true,
            message: " ",
            trigger: "blur",
          },
        ],
        implementation_user_id: [
          {
            required: true,
            message: " ",
            trigger: "blur",
          },
        ],
        over_time: [
          {
            required: true,
            message: " ",
            trigger: "blur",
          },
        ],
        train_type: [
          {
            required: true,
            message: " ",
          },
        ],
        // sell_type: [
        //   {
        //     required: true,
        //     message: "请选择销售类型",
        //   },
        // ],
        implementation_conclusion: [
          {
            required: true,
            message: " ",
            trigger: "blur",
          },
        ],
      },
      customerTypeList: [], //客户类型
      sellTypeList: [], //销售类型
      softwareVersionList: [], //软件版本
      trainTypeList: [], //培训方式
      customerStageList: [], //客户阶段
      loading: false,
      isEdit: false,
      tableData: [],
      tableList: [

      {
        width: 150,
        name: "销售合同号",
        value: "contract_no",
      },
      {

        name: "产品服务",
        value: "brandName",
      },
      {
        name: "销售类型",
        value: "detail_sell_name",
        width: 150
      },
      {
        name: "计量单位",
        value: "measurement_unit_name",
        width: 150
      },
      {
        name: "合同数量",
        value: "contract_count",
        width: 150
      },
      {
        name: "合同金额",
        value: "contract_amount",
        width: 150
      },
      {
        name: "原有端口数",
        value: "original_port_count",
        width: 150
      },
      {
        name: "新增端口数",
        value: "add_port_count",
        width: 150
      },

      {
        width: 150,
        name: "合同备注",
        value: "remark",
      },
      {
        width: 150,
        name: "软件序列号",
        value: "software_no",
      },
      {
        width: 150,
        name: "成交日期",
        value: "fixtrue_time",
      },

    ],
    }
  },
  watch:{
    'ruleForm.contract_no': {
      handler(newValue,oldValue) {
        if(newValue){
          this.getDetail()
        }

      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      this.customerTypeList = getOptions("t_customer", "customer_type");
      this.sellTypeList = getOptions("t_implementation", "sell_type");
      this.softwareVersionList = getOptions(
        "t_implementation",
        "software_version"
      );
      this.trainTypeList = getOptions("t_implementation", "train_type");
      this.isEdit = true;
      if (data) {
        this.isEdit = this.permissionToCheck("UPDATE_CUSTOMER_IMPLEMENT_NEW") 
        this.ruleForm = data;
      }
      this.$store.dispatch('getEmployee').then(res => {
				this.implementationUserList  = res
			}).catch(() => {
			})
 
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {

          // if(!this.ruleForm.sell_type){
          //   return this.error("请选择销售类型");
          // }
          if(this.ruleForm.train_type === 1){
            if(this.ruleForm.train_days < 1){
              return this.error("如果是上门培训，请填写培训天数");
            }
          }
          this.save()
        } else {
          return false;
        }
      });
    },
    dialogCancel(flag = true) {

      if(flag){
        this.dialogVisible = false;
        this.$emit("selectData");
      }
      this.clearData();
      this.resetForm("ruleForm");


    },
    save() {
      let params = this.ruleForm;
      params.customer_name = params.customer_name.replace(/\s*/g, "");
      params.voucher = this.userInfo.employeeId;
      this.loading = true;
      if (params.implementation_id) {
        if (params.auditState == 1){
          this.loading = false;
          return this.error("该单据已审核，不允许修改！");
        } 
   
        API.update(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {})
          .finally(() => {
            this.loading = false;
          });
      } else {
        API.add(params)
          .then(({data}) => {
            this.dialogCancel(false);
            this.refreshData(data)
          })
          .catch(() => {})
          .finally(() => {
            this.loading = false;
          });
      }
    },
    //新增之后，刷新当前记录
    refreshData(id){

      API.getInfo({implementation_id: id}).then(({data})=>{
        this.Show(data)
      })
    },
    //选择合同
    chooseContract() {
      this.$refs.contractList.Show();
    },
    getInfo(info) {
      // console.log(info, " === info");
      this.ruleForm.link_man = info.link_man;
      this.ruleForm.phone = info.phone;

      // this.ruleForm.address = info.address;
      this.ruleForm.customer_name = info.customer_name;
      this.ruleForm.contract_no = info.contract_no;
      this.ruleForm.customer_contract_id = info.customer_contract_id;
      this.ruleForm.fk_customer_id = info.fk_customer_id;
      this.ruleForm.fk_sell_employee_id = info.fk_sell_employee_id;
      this.ruleForm.fk_sell_employee_name = info.fk_sell_employee_name;
      this.ruleForm.train_type = info.train_type;
      this.ruleForm.sell_type = info.sell_type;

      // this.ruleForm.customer_type = info.customer_type;
    },
    add() {

      this.$refs.fileList.Show();
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        customer_contract_id: "",
        contract_no: "",
        customer_name: "",
        fk_sell_employee_id: "",
        fk_sell_employee_name: "",
        fk_customer_id: "",
        link_man: "",
        // address: "",
        phone: "",
        // customer_type: "",
        implementation_time: "",
        over_time: null,
        train_type: "",
        implementation_conclusion: "",
        original_port_count: "",
        add_port_count: "",
        version_no: "",
        follow_records: "",
        remark: "",
        attention_matters: "",
      };
      this.tableData = []
    },
    getDetail(){


      if(!this.ruleForm.customer_contract_id){
        return
      }
      API2.detailList({
        customer_contract_id: this.ruleForm.customer_contract_id,
        ditail_tyoe : 1
      }).then(({data})=>{

        this.tableData = data
      })

    },
    addNew(){
      this.dialogCancel(false);
    },
    what(e) {
      this.$forceUpdate()
    },
  },
  computed: {
    ...mapGetters([
      "userInfo",
      "buttonPermissions",
      "sell_type",
      "contract_train_type",
      "software_version",
    ]),
  },
};
