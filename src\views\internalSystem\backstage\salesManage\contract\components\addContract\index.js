import API from "@/api/internalSystem/salesManage/contract";
import API2 from "@/api/internalSystem/customerManage/customerInfo";
import fileAPI from "@/api/internalSystem/common/enclosure.js";
import quoAPI from "@/api/internalSystem/salesManage/quotation";
import paramAPI from "@/api/internalSystem/basicManage/parameter";
import brandAPI from "@/api/internalSystem/basicManage/brand";
import salesUnitAPI from '@/api/internalSystem/basicManage/salesUnit'
import CustomerList from "@/views/internalSystem/backstage/components/customerList/index.vue";
import CustomerBrandList from "@/views/internalSystem/backstage/components/customerBrandList/index.vue";
// import SalesUnitList from "@/views/internalSystem/backstage/components/salesUnitList/index.vue";
import ContractList from "@/views/internalSystem/backstage/components/contractList/index.vue";
import QuotationList from "../quotationList/index.vue";
import FileList from "@/views/internalSystem/backstage/components/fileList/index.vue";
import TableCustom from "@/views/internalSystem/backstage/components/tableCustom/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import { dateFormat } from "@/common/internalSystem/common.js";
import { mapGetters } from "vuex";
import { checkMaintenanceFee } from "@/utils/calculate.js";
import { difference } from "lodash";
import moment from "moment";
export default {
  name: "addContract",
  components: {
    CustomerList,
    // SalesUnitList,
    ContractList,
    QuotationList,
    FileList,
    TableCustom,
    MyDate,
    CustomerBrandList,
  },
  props: {
    // xufeiObj: {
    //   type: Array,
    //   default: () => Object
    // },
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        customer_name: "",
        fk_customer_id: "",
        train_type: 2,
        pay_type: 1,
        sell_type: "",
        sales_unit_id: "",
        link_man: "",
        fk_sell_employee_id: "",
        fk_sell_department_id: "",
        phone: "",
        introducer_format: "",
        introducer: "",
        province: "",
        city: "",
        remark: "",
        address: "",
        fax: "",
        link_qq: "",
        sales_unit_id_format: "",
        introducer_contract_format: "",
        introducer_contract_id: "",
        fk_recommend_employee_id: "",
        renewal_contract_format: "",
        renewal_contract_id: "",
        deal_time: "",
      },
      rules: {
        customer_name: [
          {
            required: true,
            message: " ",
          },
        ],
        sales_unit_id_format: [
          {
            required: true,
            message: " ",
          },
        ],
        train_type: [
          {
            required: true,
            message: " ",
          },
        ],
        pay_type: [
          {
            required: true,
            message: " ",
            trigger: "change",
          },
        ],
        // sell_type: [{
        //   required: true,
        //   message: " ",
        //   trigger: "change",
        // } ],
        link_man: [
          {
            required: true,
            message: " ",
          },
        ],
        phone: [
          {
            required: true,
            message: " ",
          },
        ],
        sales_unit_id: [
          {
            required: true,
            message: " ",
          },
        ],
        link_qq: [
          {
            required: true,
            message: " ",
          },
        ],
        address: [
          {
            required: true,
            message: " ",
          },
        ],
      },
      provinceList: [], //省
      cityList: [], //市
      softwareVersionList: [], //软件版本
      measurementUnitList: [], //计量单位
      paramsList: [],
      loading: false,
      activeName: "first",
      proList: [], //产品列表
      proAllList: [], //全部产品列表
      moduleList: [], //模块列表
      quotationRateList: [], //报价税率
      prepaymentRatioList: [], //预付款比例
      yearsFeeList: [], //年维护费比例
      employeeList: [],
      salesUnits: [],
      proTableCol: [
        {
          label: "id",
          prop: "quotation_id",
          isHide: true,
          isEditConfig: true,
        },
        {
          label: "产品服务",
          prop: "fk_brand_id",
          need: true,
          isEditConfig: true,
          width: 220,
        },
        // {
        //   label: "产品销售类型",
        //   prop: "product_sell_type",
        //   need: true,
        //   width: 180,
        // },
        {
          label: "销售类型",
          prop: "detail_sell_type",
          need: true,
          isEditConfig: true,
          width: 180,
        },
        // {
        //   label: "原产品服务",
        //   prop: "old_fk_brand_id",
        //   width: 370,
        //   disabled: true,
        // },
        // {
        //   label: "软件版本",
        //   prop: "software_version",
        //   need: true,
        //   width: 160,
        // },

        {
          label: "计量单位",
          prop: "measurement_unit",
          need: true,
          isEditConfig: true,
          width: 160,
        },
        {
          label: "合同数量",
          prop: "contract_count",
          need: true,
          isEditConfig: true,
          width: 160,
          fn: "f3",
        },
        {
          label: "合同单价",
          prop: "contract_price",
          need: true,
          isEditConfig: true,
          width: 160,
        },

        {
          label: "合同金额",
          prop: "contract_amount",
          need: true,
          isEditConfig: true,
          width: 160,
        },
        {
          label: "发票税率",
          prop: "invoice_tax_rate",
          need: true,
          isEditConfig: true,
          width: 160,
          isJoin: true,
        },
        // {
        //   label: "预付款比例%",
        //   prop: "prepayment_rate",
        //   need: true,
        //   width: 160,
        //   isJoin: true
        // },
        {
          label: "年维护费比例%",
          prop: "year_maintain_cost",
          need: true,
          isEditConfig: true,
          width: 160,
          isJoin: true,
        },
        {
          label: "年维护费",
          prop: "maintenance_fee",
          need: true,
          isEditConfig: true,
          width: 160,
        },
        // {
        //   label: "预付款期限",
        //   prop: "prepayment_time_limit",
        //   width: 160,
        // },
        {
          label: "原维护结束日期",
          prop: "original_maintain_stop_time",
          width: 160,
        },
        {
          label: "维护起始日期",
          prop: "maintain_start_time",
          need: true,
          isEditConfig: true,
          width: 160,
        },
        {
          label: "新维护结束日期",
          prop: "new_maintain_stop_time",
          need: true,
          isEditConfig: true,
          width: 160,
        },

        {
          label: "原有端口数",
          prop: "original_port_count",

          width: 160,
        },
        {
          label: "新增端口数",
          prop: "add_port_count",
          isEditConfig: true,
          width: 160,
        },
        {
          label: "合同备注",
          prop: "remark",
          isEditConfig: true,
          width: 160,
        },
        {
          label: "授权码",
          prop: "software_no",
          width: 160,
        },
      ],
      proObj: {},
      moduleTableCol: [
        {
          label: "id",
          prop: "quotation_id",
          isHide: true,
        },
        {
          label: "模块名称",
          prop: "fk_brand_id",
          need: true,
        },
        {
          label: "计量单位",
          prop: "measurement_unit",
          need: true,
        },
        {
          label: "合同金额",
          prop: "contract_amount",
          need: true,
        },
        {
          label: "合同备注",
          prop: "remark",
        },
        {
          label: "出库数量",
          prop: "stock_removal_count",
        },
      ],
      moduleObj: {},
      isEdit: false,
      isOwn: true,
      dialogVisibleTem: false,
      temForm: {
        contract_template_id: "",
      },
      temRules: {
        contract_template_id: [
          {
            required: true,
            message: "请选择合同模板",
            trigger: "change",
          },
        ],
      },
      temList: [],
      command: "",
      temObj: {
        1: ["软件购买合同书", "软件服务协议(首次)"],
        2: ["软件购买合同书(二次开发)"],
        3: ["软件服务协议(维护费<1500)", "软件服务协议(维护费>1500)"],
        4: ["软件购买合同书(增加端口)"],
        5: ["吉勤软件租用合同"],
        6: ["软件购买合同书(软件升级)"],
        7: ["软件购买合同书(二次销售)"],
        8: ["吉勤软件租用合同(金万维)"],
      },
      isAdd: true,
      first_contract: false, // 是否第一次制作合同
      visible: false, //调入开关
      TmpSellType: 0,
      byRouter: false, // 通过路由进来的
      xufeiObj: null,
      // customerBrandFlag: false,
      contract_brand_detail: [],
      contractDetailIds: [],
      customerIds: null,
      fileCount: 0,
      dialogVisibleAudit: false,
      auditForm: {
        auditState: 1,
        auditRemark: "",
      },
      auditRules: {
        auditState: [
          {
            required: true,
            message: "请选择审核状态",
            trigger: "change",
          },
        ],
      },
      total_money:0,
      fileImg:
        "http://trade-erp.oss-cn-beijing.aliyuncs.com/%E7%A6%8F%E5%B7%9E%E5%90%89%E5%8B%A4%E4%BF%A1%E6%81%AF%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8/XSDD/SZNp4YWa_2741_system_%E5%B7%B2%E5%AE%8C%E6%88%90.png",
    };

  },
  created() {},

  async mounted() {},

  methods: {
    openAudit() {
      console.log(this.ruleForm.audit_state);
      if(this.ruleForm.audit_state !== 3) {
        return this.error("不是待审核状态")
      }

      this.dialogVisibleAudit = true;
    },
    async xufei(row, sellType) {
      if (this.xufeiObj !== null) {
        this.dialogCancel(false, false);
      }
      this.xufeiObj = row;
      //针对首页跳转过来的续费

      // let sell_type = sellType

      if (!this.sell_type || (this.sell_type && this.sell_type.length === 0)) {
        // setTimeout(() => {}, 1000);
      }
      this.byRouter = true;
      //查合同详情
      // let contractData = await API.query({
      //   isJurisdiction: 1,
      //   pageNum: 1,
      //   pageSize: 1,
      //   customer_contract_id: row.customerContractId
      // })

      this.Show();
      let customerData = await API2.query({
        pageNum: 1,
        pageSize: 1,
        customer_id: row.fkCustomerId,
      });
      this.getCustomerInfo(customerData.data[0], null, sellType);
    },
    closeFile(fileId) {
      this.getFileCount(fileId);
    },

    // 统计该合同的附件数
    getFileCount(fileId) {
      fileAPI.contractFileList({ id: fileId }).then((res) => {
        this.fileCount = res.totalCount || 0;
      });
    },
    async Show(data = null, type = null) {
      this.getSalesUnit()
      this.total_money = 0
      if (type === "authorization") {
        if (this.$refs.proTableCustom) {
          this.$refs.proTableCustom.clear();
        }
      }

      if (data) {
        this.proTableCol[2].isHide =
          this.ruleForm.audit_state === 4 ? true : false;
        this.getFileCount(data.customer_contract_id);
        this.isAdd = false;
      } else {
        this.isAdd = true;
        this.getCampanyInfo();
      }
      await this.getParam();
      this.proList = [];
      this.moduleList = [];
      this.quotationRateList = [];
      this.prepaymentRatioList = [];
      this.yearsFeeList = [];
      this.dialogVisible = true;
      // this.getProvinceList();
      this.activeName = "first";
      this.$store.dispatch("getEmployee").then((res) => {
        this.employeeList = res;
      });
      this.ruleForm.add_user_name = this.userInfo.fullName;
      this.ruleForm.fk_operator_department_name = this.userInfo.department_name;
      this.isEdit = true;
      this.isOwn = true;
      await this.getBrand(data);

      this.proObjRest();

      if (data) {
        this.ruleForm = data;
          console.log(data);
        this.isEdit =
          this.permissionToCheck("UPDATE_CONTRACT_NEW") &&
          (data.audit_state == 4 || data.audit_state == 2);
        this.isOwn = (data.update_user_id === this.userInfo.userId  || data.fk_sell_employee_id == this.userInfo.userId )? true : false;
        // this.getCityList();
        API.detailList({
          customer_contract_id: data.customer_contract_id,
        })
          .then((res) => {
            res.data.map((item) => {
              let pro = JSON.parse(JSON.stringify(this.proObj));
              // let module = JSON.parse(JSON.stringify(this.moduleObj));
              for (let v in item) {
                if (item.detail_type == 1) {
                  if (pro[v]) {
                    pro[v].value = item[v];
                    pro[v].disabled = !this.isEdit || !this.isOwn;
                  }
                } else {
                  // if (module[v]) {
                  //   module[v].value = item[v];
                  //   module[v].disabled = !this.isEdit || !this.isOwn;
                  // }
                }
              }
              //授权码一定是禁止填写
              pro.year_maintain_cost.value = pro.year_maintain_cost.value + "";
              pro.invoice_tax_rate.value = pro.invoice_tax_rate.value + "";
              pro.original_maintain_stop_time.disabled = true;
              pro.software_no.disabled = true;
              if (item.detail_type == 1) {
                this.$refs.proTableCustom.add2(pro);
              }
              // else {
              //   this.$refs.moduleTableCustom.add2(module);
              // }
            });
          })
          .catch(() => {})
          .finally(() => {
            //页面都赋值结束，再修改
            // this.isAdd = true
          });
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (formName === "ruleForm") {
            this.checkTax();


            // this.save();
          } else if (formName === "auditForm") {
            this.save2();
          }
        } else {
          return false;
        }
      });
    },
    async checkTax(){
      let proList =  []
      try {
        proList = await this.$refs.proTableCustom.getData()
      } catch {
        this.activeName = "first";
        return;
      }
      if (proList.length == 0) return this.error("至少有一条数据");
      let invoice_tax_rate_flag = false;
      proList.map((item) => {
        if (
          !item.invoice_tax_rate.value ||
          Number(item.invoice_tax_rate.value) === 0
        ) {
          invoice_tax_rate_flag = true;
        }
      });
      if (invoice_tax_rate_flag) {
        this.$confirm("存在开票税率是 0 的明细, 是否继续保存?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.save(proList);
          })
          .catch(() => {});
      } else {
        this.save(proList);
      }
    },
    save2() {
      let params = this.auditForm;
      params.customer_contract_id = this.ruleForm.customer_contract_id;
      this.loading = true;
      API.updateAudit(params)
        .then(() => {
          this.dialogCancelAudit();
          this.dialogCancel(true);
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },
    //返回 新增
    dialogCancel(flag = true, flag2 = true) {
      this.contractDetailIds = [];
      this.customerIds = null;
      this.TmpSellType = 0;
      if (flag) {
        this.dialogVisible = false;
        this.$emit("selectData");
      }
      if (flag2) {
        this.resetForm("ruleForm");
      }

      this.clearData();

      if (!flag) {
        this.Show();
        if (this.$refs.proTableCustom) {
          this.$refs.proTableCustom.clear();
        }
      }
    },
    async save(proList) {
      let params = this.ruleForm;
      const reg = /^[1-9]\d*$/;
      if (params.train_type === 1) {
        if (!params.train_days) {
          return this.error("上门培训，请填写培训天数");
        }
        if (!reg.test(params.train_days)) {
          return this.error("培训天数为正整数");
        }
        if (params.train_days < 1) {
          return this.error("培训天数至少1天");
        }
      }
      if (!params.phone) {
        return this.error("手机不能为空");
      }
      if (params.phone && params.phone.length != 11) {
        return this.error("手机号码必须是11位");
      }
      if (params.phone && !/^1[3|4|5|6|7|8|9]\d{9}$/.test(params.phone)) {
        return this.error("手机号码格式不对");
      }

      // 8 = 赠送端口  只需要知道介绍合同，自己本身就是介绍人
      if (params.sell_type == "8") {
        // if (!params.introducer) return this.error("请选择介绍人");
        if (!params.introducer_contract_id) return this.error("请选择介绍合同");
      }
      params.fk_operator_department_id = this.userInfo.department_id;
      // let proList = [];
      // try {
      //   proList = await this.$refs.proTableCustom.getData();
      // } catch {
      //   this.activeName = "first";
      //   return;
      // }
      // let moduleList = [];
      // try {
      //   moduleList = await this.$refs.moduleTableCustom.getData();
      // } catch {
      //   this.activeName = "second";
      //   return;
      // }

      let detail = [];
      if (proList.length == 0) return this.error("产品至少有一条数据");
      // if (moduleList.length == 0) return this.error("模块至少有一条数据");
      let quotation_ids = [];
      let f = false;
      // let proMoney = 0
      //   moduleMoney = 0;
      // let unitFlag = true;
      // let prepaymentRateFlag = true; //预付款判断
      // let yearMaintainCostFlag = true; //年维护费判断
      // let brandIdFlag = false; //年维护费判断
      // let measurementUnitFlag = true;

      let distinctBrandId = [];

      proList.map((item) => {
        distinctBrandId.push(item.fk_brand_id.value);
      });

      distinctBrandId = Array.from(new Set(distinctBrandId));
      if (distinctBrandId.length !== proList.length) {
        return this.error("存在相同的产品，请修改产品");
      }

      for (let i = 0; i < proList.length; i++) {
        let item = proList[i];

        // if (
          // item.year_maintain_cost.value === "100" &&
          // item.measurement_unit.value !== 4
        // ) {
          // measurementUnitFlag = false;
        // }
        // 判断时间
        if(!item.maintain_start_time.value ){
          return this.error(
            "第" + (i + 1) + "条明细 的开始时间为空，请检查时间"
          );
        }

        if(!item.new_maintain_stop_time.value ){
          return this.error(
            "第" + (i + 1) + "条明细 的结束时间为空，请检查时间"
          );
        }

        if(item.maintain_start_time.value == '1970-01-01' || item.new_maintain_stop_time.value == '1970-01-01' ){
          return this.error(
            "第" + (i + 1) + "条明细 的时间是1970-01-01，请检查时间"
          );
        }

        if (item.quotation_id.value) {
          quotation_ids.push(item.quotation_id.value);
        }

        // 4 = 增加端口 8 = 赠送端口
        if (item.measurement_unit.value == "0") {
          // unitFlag = false;
          return this.error("计量单位请选择个！");
        }
        if (
          item.detail_sell_type.value &&
          [4,8].includes(Number(item.detail_sell_type.value))
        ) {
          if (
            !item.add_port_count.value ||
            Number(item.add_port_count.value) === 0
          ) {
            f = true;
            return this.error("新增端口有误，请重新输入");
          }
        }
        // 租用和销售
        if ([1, 5].includes(item.detail_sell_type.value)) {
          //计量单位是年
          if (item.measurement_unit.value === 4) {
            if (
              moment(item.new_maintain_stop_time.value).diff(
                moment(item.maintain_start_time.value),
                "days"
              ) <
              item.contract_count.value * 360
            ) {
              return this.error(
                "第" + (i + 1) + "条明细 合同数 和 合同年数 不匹配"
              );
            }
          }
        }

        // proMoney += parseFloat(item.contract_amount.value);
        detail.push({
          fk_brand_id: item.fk_brand_id.value,
          // software_version: item.software_version.value,
          measurement_unit: item.measurement_unit.value,
          contract_price: item.contract_price.value,
          contract_count: item.contract_count.value,
          contract_amount: item.contract_amount.value,
          invoice_tax_rate: item.invoice_tax_rate.value,
          // prepayment_rate: item.prepayment_rate.value,
          year_maintain_cost: item.year_maintain_cost.value,
          // prepayment_time_limit: item.prepayment_time_limit.value,
          maintain_start_time: dateFormat(
            "yyyy-MM-dd",
            new Date(item.maintain_start_time.value)
          ),
          new_maintain_stop_time: item.new_maintain_stop_time.value,
          original_maintain_stop_time: item.original_maintain_stop_time.value
            ? dateFormat(
                "yyyy-MM-dd",
                new Date(item.original_maintain_stop_time.value)
              )
            : null,
          original_port_count: item.original_port_count.value,
          add_port_count: item.add_port_count.value,
          remark: item.remark.value,
          // product_sell_type: item.product_sell_type.value,
          detail_sell_type: item.detail_sell_type.value,
          maintenance_fee: item.maintenance_fee.value,

          //item.contract_count.value && item.year_maintain_cost.value ?
          //item.contract_count.value * item.year_maintain_cost.value / 100 : 0, //年维护费
          detail_type: 1,
        });
      }

      // if (!prepaymentRateFlag) {
      //   return this.error('预付款比例有误')
      // }
      // if (!yearMaintainCostFlag) {
      //   return this.error('销售类型是租用软件，年维护费必须为100%')
      // }

      // if (!measurementUnitFlag) {
      //   return this.error('年维护费是100%时，计量单位请选择年')
      // }
      if (f) return this.error("请填写新增端口数！");
      // if (!unitFlag) return this.error("计量单位请选择个！");

      quotation_ids = Array.from(new Set(quotation_ids));
      params.quotation_ids = quotation_ids.join(",");
      params.detail = detail;
      let maintain_start_time = new Date(detail[0].maintain_start_time);
      let maintain_stop_time = new Date(detail[0].new_maintain_stop_time);
      detail.forEach((item) => {
        let min = new Date(item.maintain_start_time);
        let max = new Date(item.new_maintain_stop_time);
        if (min < maintain_start_time) {
          maintain_start_time = min;
        }
        if (maintain_stop_time < max) {
          maintain_stop_time = max;
        }
      });
      params.maintain_start_time = dateFormat(
        "yyyy-MM-dd",
        maintain_start_time
      );
      params.maintain_stop_time = dateFormat("yyyy-MM-dd", maintain_stop_time);
      params.audit_state = 4;

      this.loading = true;

      if (params.customer_contract_id) {
        if (this.ruleForm.audit_state != 4 && this.ruleForm.audit_state != 2)
          return this.error("该单据已发出审核，不允许修改！");
        API.update(params)
          .then(() => {
            this.dialogCancel();
            this.$emit("getInfo", params.customer_contract_id);
          })
          .catch(() => {})
          .finally(() => {
            this.loading = false;
          });
      } else {
        API.add(params)
          .then((res) => {
            this.dialogCancel();
            this.$emit("getInfo", res.data);
          })
          .catch(() => {})
          .finally(() => {
            this.loading = false;
          });
      }
    },
    send() {
      if (this.ruleForm.data_state === 2) {
        return this.error("该记录是旧数据，请上旧系统继续操作");
      }
      this.$confirm("此操作将发出该条记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            customer_contract_id: this.ruleForm.customer_contract_id,
            auditState: 3,
          };
          API.updateAudit(params)
            .then(() => {
              this.dialogCancel(false);
            })
            .catch(() => {})
            .finally(() => {
              // this.loading = false;
            });
        })
        .catch(() => {});
    },
    back() {
      if (this.ruleForm.data_state === 2) {
        return this.error("该记录是旧数据，请上旧系统继续操作");
      }
      this.$confirm("此操作将回退该条记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            customer_contract_id: this.ruleForm.customer_contract_id,
            auditState: 3,
          };
          API.updateAudit(params)
            .then(() => {
              this.dialogCancel();
            })
            .catch(() => {})
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {});
    },
    addFile() {
      if (this.ruleForm.data_state === 2) {
        return this.error("该记录是旧数据，请上旧系统继续操作");
      }
      this.$refs.fileList.Show();
    },
    getBrand(data) {
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      return new Promise((resolve, reject) => {
        // this.isAdd?{disable_state:1}:{}
        brandAPI[this.isAdd || data.audit_state === 4 ? "query2" : "query"](this.isAdd?{disable_state:1}:{})
          .then((data) => {
            data.data.forEach((item) => {
              // if (item.brand_classify == 1)
              this.proList.push({
                label: item.brand_type,
                value: item.brand_id,
                parentId: item.parent_id,
              });
            });
            resolve(1);
            loading.close();
          })
          .catch(() => {
            reject();
            loading.close();
          });
        //查询全部，用于原有产品下拉框
        // brandAPI["query"]()
        //   .then((data) => {
        //     data.data.forEach((item) => {
        //       if (item.brand_classify == 1)
        //         this.proAllList.push({
        //           label: item.brand_type,
        //           value: item.brand_id,
        //         });
        //     });
        //     resolve(1);
        //     loading.close();
        //   })
        //   .catch(() => {
        //     reject();
        //     loading.close();
        //   });
      });
    },
    getSalesUnit() {
      salesUnitAPI["query"]().then((data) => {
        this.salesUnits = data.data
      });
    },
    openContractTemplate() {
      if (this.ruleForm.data_state === 2) {
        return this.error("该记录是旧数据，请上旧系统继续操作");
      }
      if (!this.ruleForm.customer_contract_id) {
        return;
      }
      this.command = "select";
      this.dialogVisibleTem = true;
    },
    handleCommand(command) {
      if (this.ruleForm.data_state === 2) {
        return this.error("该记录是旧数据，请上旧系统继续操作");
      }
      this.command = command;
      this.dialogVisibleTem = true;
      API.templateList()
        .then((data) => {
          let option = [];
          data.data.map((item) => {
            if (
              this.temObj[this.ruleForm.sell_type].some(
                (ele) => ele === item.contract_template_name
              )
            ) {
              option.push(item);
            }
          });
          this.temList = option;
        })
        .catch(() => {});
    },
    submitTem(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let url =
            "/internalSystem/#/print?type=" +
            this.command +
            "&contract_template_id=" +
            this.temForm.contract_template_id +
            "&customer_contract_id=" +
            this.ruleForm.customer_contract_id;
          window.open(url, "_blank");
          this.dialogCancelTem();
          this.temForm.contract_template_id = "";
        } else {
          return false;
        }
      });
    },
    dialogCancelTem() {
      this.dialogVisibleTem = false;
    },
    getParam() {
      paramAPI
        .query()
        .then((data) => {
          data.data.forEach((item) => {
            if (item.parameter_type == 5)
              this.quotationRateList.push({
                label: item.content,
                value: item.content,
              });
            else if (item.parameter_type == 1)
              this.prepaymentRatioList.push({
                label: item.content,
                value: item.content,
              });
            else if (item.parameter_type == 2)
              this.yearsFeeList.push({
                label: item.content,
                value: item.content,
              });
          });
        })
        .catch(() => {});
    },
    // 产品 同步 功能
    // async proSameModule() {
    //   let proList = [];
    //   try {
    //     proList = await this.$refs.proTableCustom.getData();
    //   } catch {
    //     this.activeName = "first";
    //     return;
    //   }
    //   // if (proList.length !== 0) {
    //   //   this.$refs.moduleTableCustom.proSameModule(proList)
    //   // }

    // },
    /**
     * 客户产品信息表选择
     * @param {*} info 勾选产品
     * @param {*} customerIds  客户id，判重用
     * @param {*} contractDetailIds 产品id，判重用
     */
    async getCustomerBrandInfo(
      info = [],
      customerIds = null,
      contractDetailIds = []
    ) {
      this.customerIds = customerIds;
      this.contractDetailIds = contractDetailIds;

      let customerData = await API2.query({
        pageNum: 1,
        pageSize: 1,
        customer_id: customerIds,
      });
      this.getCustomerInfo(customerData.data[0], "01");

      for (let i = 0; i < info.length; i++) {
        this.add(info[i], "04");
      }
    },
    async add(data = {}, flag = null, sellType = null) {
      console.log(flag)
      // if (!this.ruleForm.fk_customer_id) return this.error('请选择客户')

      // if (this.activeName === "first") {
      // 3 = 软件服务费 初始化起始时间  结束时间
      // console.log(data);
      // if (this.ruleForm.sell_type == "3") {
      //   let d = new Date();
      //   let pro = JSON.parse(JSON.stringify(this.proObj));
      //   // console.log(pro, ' === pro' );
      //   if (data.proObj) {
      //     pro.contract_amount.value = data.sumMoney
      //     // pro.software_version.value = data.proObj['software_version']
      //     pro.fk_brand_id.value = data.proObj['fk_brand_id']
      //     pro.original_port_count.value = this.ruleForm.port_number
      //     d = new Date(data.proObj['new_maintain_stop_time'])
      //     pro.original_maintain_stop_time.value = dateFormat("yyyy-MM-dd", d)
      //     // pro['old_fk_brand_id'].value = data.proObj['old_fk_brand_id']?data.proObj.old_fk_brand_id:null
      //     pro['fk_brand_id'].value = data.proObj['old_fk_brand_id'] === 15 ? 48 : 47
      //   }
      //   pro.maintain_start_time.value = dateFormat("yyyy-MM-dd", d);
      //   d.setFullYear(d.getFullYear() + 1);
      //   pro.new_maintain_stop_time.value = dateFormat("yyyy-MM-dd", d);

      //   this.$refs.proTableCustom.add2(pro);
      // }else

      // if (this.TmpSellType == "5") {
      //   //销售类型是租用合同 赋值基本值
      //   let d = new Date();
      //   let pro = JSON.parse(JSON.stringify(this.proObj));
      //   if (data) {
      //     pro.contract_amount.value = data.contract_amount
      //     pro.fk_brand_id.value = data['fk_brand_id']
      //     pro.original_port_count.value = getMainBrand('list').includes(data['fk_brand_id']) ? this.ruleForm.port_number : 0
      //     pro.detail_sell_type.value = 5
      //     d = new Date(data['new_maintain_stop_time'])
      //     pro.original_maintain_stop_time.value = dateFormat("yyyy-MM-dd", d)
      //     if (data.contract_detail_id) {
      //       pro.tmpContractDetailId = data.customer_brand_id
      //     }
      //   }

      //   pro.maintain_start_time.value = dateFormat("yyyy-MM-dd", d);
      //   d.setFullYear(d.getFullYear() + 1);
      //   pro.new_maintain_stop_time.value = dateFormat("yyyy-MM-dd", new Date(d.getTime() - 24 * 60 * 60 * 1000));
      //   pro.year_maintain_cost.value = pro.year_maintain_cost.value + ""
      //   pro.invoice_tax_rate.value = pro.invoice_tax_rate.value + ""

      //   this.$refs.proTableCustom.add2(pro);
      // }
      // else if (this.ruleForm.sel_type === 7 && flag === '03') {
      //   let pro = JSON.parse(JSON.stringify(this.proObj));
      //   for (let v in data) {
      //     if (pro[v]) {
      //       pro[v].value = data[v];
      //     }
      //   }

      //   let d = new Date();
      //   d = new Date(data['new_maintain_stop_time'])
      //   pro.original_maintain_stop_time.value = dateFormat("yyyy-MM-dd", d)
      //   pro.maintain_start_time.value = dateFormat("yyyy-MM-dd", d);
      //   d.setFullYear(d.getFullYear() + 1);
      //   pro.new_maintain_stop_time.value = dateFormat("yyyy-MM-dd", d);
      //   pro['fk_brand_id'].value = pro['old_fk_brand_id'] === 15 ? 48 : 47
      //   //查出客户的主产品
      //   this.$refs.proTableCustom.add2(pro);
      // }
      // else
      if (flag === "04") {
        let res = await brandAPI.getPortBrand();

        //从客户产品选择的
        let pro = JSON.parse(JSON.stringify(this.proObj));
        for (let v in data) {
          if (pro[v]) {
            pro[v].value = data[v];
          }
        }
        //避免重复产品
        if (data.customer_brand_id) {
          pro.tmpContractDetailId = data.customer_brand_id;
        }

        pro.original_port_count.value = res.data.includes(data["fk_brand_id"])
          ? this.ruleForm.port_number
          : 0; //原有端口
        // pro.original_port_count.value = this.ruleForm.port_number
        pro.contract_price.value = pro.maintenance_fee.value + "";
        pro.year_maintain_cost.value = pro.year_maintain_cost.value + "";
        pro.invoice_tax_rate.value = pro.invoice_tax_rate.value + "";

        this.$refs.proTableCustom.add2(pro);
      } else if (flag === "05") {
        // console.log(flag, sellType);
        //选择客户之后，调入客户主产品
        let pro = JSON.parse(JSON.stringify(this.proObj));

        for (let v in data) {
          if (pro[v]) {
            pro[v].value = data[v];
          }
        }

        pro.year_maintain_cost.value = pro.year_maintain_cost.value + "";
        pro.invoice_tax_rate.value = pro.invoice_tax_rate.value
          ? pro.invoice_tax_rate.value + ""
          : "0";

        pro.detail_sell_type.value = pro.detail_sell_type.value
          ? pro.detail_sell_type.value
          : 4;
        pro.measurement_unit.value = 1;

        // pro.contract_price.value = 0;
        // pro.contract_amount.value = 0;
        // pro.maintenance_fee.value = 0;
        if (sellType) {
          pro.original_maintain_stop_time.value =
            pro.new_maintain_stop_time.value;
          pro.maintain_start_time.value = pro.new_maintain_stop_time.value;
          let d = new Date(pro.new_maintain_stop_time.value);
          d.setFullYear(d.getFullYear() + 1);
          pro.new_maintain_stop_time.value = dateFormat(
            "yyyy-MM-dd",
            new Date(d.getTime() ) // 去掉少一天 - 24 * 60 * 60 * 1000
          );
          pro.contract_amount.value = pro.maintenance_fee.value;
          pro.contract_price.value = pro.maintenance_fee.value;
          if (Number(pro.maintenance_fee.value) === 0) {
            pro.maintenance_fee.value = this.checkMaintenanceFee(pro);
          }
        } else {
          // pro.new_maintain_stop_time.value = data.new_maintain_stop_time;
          pro.contract_price.value = 0;
          pro.contract_amount.value = 0;
          pro.maintenance_fee.value = 0;
        }
        let res = await brandAPI.getPortBrand();

        pro.original_port_count.value = res.data.includes(data["fk_brand_id"])
          ? this.ruleForm.port_number
          : 0; //原有端口//this.ruleForm.port_number

        this.$refs.proTableCustom.add2(pro);
      } else {
        //点击新增产品
        let pro = JSON.parse(JSON.stringify(this.proObj));
        let d = new Date();
        pro.maintain_start_time.value = dateFormat("yyyy-MM-dd", d);
        d.setFullYear(d.getFullYear() + 1);
        pro.new_maintain_stop_time.value = dateFormat(
          "yyyy-MM-dd",
          new Date(d.getTime()) //  去掉少一天 - 24 * 60 * 60 * 1000
        );
        let res = await brandAPI.getPortBrand();
        pro.original_port_count.value = res.data.includes(data["fk_brand_id"])
          ? this.ruleForm.port_number
          : 0; //原有端口//this.ruleForm.port_number //原有端口
        this.$refs.proTableCustom.add2(pro);
      }
      // }
      // else if (this.activeName === "second") {
      //   this.$refs.moduleTableCustom.add();
      // }
    },
    del(info = {}) {
      //去掉
      if (info.tmpContractDetailId) {
        this.contractDetailIds = difference(this.contractDetailIds, [
          info.tmpContractDetailId,
        ]);
      }
      if (info.quotation_id.value) {
        let proList = this.$refs.proTableCustom.getData2();
        this.contractDetailIds = [];
        // let moduleList = this.$refs.moduleTableCustom.getData2();
        proList.forEach((item) => {
          if (item.quotation_id.value == info.quotation_id.value)
            this.$refs.proTableCustom.delBy(
              "quotation_id",
              info.quotation_id.value
            );
        });
        // moduleList.forEach((item) => {
        //   if (item.quotation_id.value == info.quotation_id.value)
        //     this.$refs.moduleTableCustom.delBy(
        //       "quotation_id",
        //       info.quotation_id.value
        //     );
        // });
      }
    },
    del2() {
      if (this.ruleForm.data_state === 2) {
        return this.error("该记录是旧数据，请上旧系统继续操作");
      }
      if (this.ruleForm.audit_state == 1 || this.ruleForm.audit_state == 0)
        return this.error("该单据已发出审核，不允许删除");
      this.$confirm("是否删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            customer_contract_id: this.ruleForm.customer_contract_id,
          };
          API.remove(params)
            .then(() => {
              this.getList();
            })
            .catch(() => {});
          this.dialogCancel(false);
        })
        .catch(() => {});
    },
    //选择介绍人
    chooseIntroducer() {
      this.$refs.dealCustomerList.Show();
    },
    //获取介绍人
    getInfo(info = {}) {
      this.ruleForm.introducer_format = info.customer_name;
      this.ruleForm.introducer = info.customer_id;
    },
    //选择客户
    chooseCustomer(sell_type) {
      // if ([3, 5].includes(sell_type)) {
      //   this.$refs.customerList.Show();
      // } else {
      this.$refs.customerAllList.Show();
      // }
    },
    //客户列表选择
    async getCustomerInfo(info = {}, flag = null, sellType = null) {
      console.log(flag)
      let { data } = await brandAPI.getPortBrand();
      this.ruleForm.software_no = info.software_no;
      this.ruleForm.customer_name = info.customer_name;
      this.ruleForm.fk_customer_id = info.customer_id;
      this.ruleForm.fk_sell_employee_id = info.fk_sale_employee_id;
      this.ruleForm.fk_sell_department_id = info.department_id;
      this.ruleForm.fk_sell_employee_name = info.fk_sale_employee_id_name;
      this.ruleForm.fk_sell_department_name = info.department_name;
      this.ruleForm.link_man = info.link_man;
      this.ruleForm.address = info.link_address;
      this.ruleForm.fax = info.fax;
      this.ruleForm.province = info.province;
      this.ruleForm.deal_time = info.deal_time
        ? info.deal_time
        : info.create_time;
      this.ruleForm.sure_port_number = info.sure_port_number;

      // if (info.province) {
      // this.getCityList();
      // }
      this.ruleForm.city = info.city;
      this.ruleForm.link_qq = info.qq;
      // if (info.phone) {
      //   this.ruleForm.phone = info.phone;
      // } else {
      this.ruleForm.phone = info.telephone;
      // }
      this.ruleForm.introducer_format = info.introducer_format;
      this.ruleForm.introducer = info.introducer;
      this.ruleForm.port_number = info.sure_port_number; // 端口数  以产品表为准
      this.proObj.original_port_count.value = info.sure_port_number; // 端口数  以产品表为准
      // this.ruleForm.port_number = info.port_number;
      // this.proObj.original_port_count.value = info.port_number; // 端口数
      let pro = JSON.parse(JSON.stringify(this.proObj));
      // console.log(info);
      // console.log(" info === ");
      pro.maintain_start_time.value = info.maintain_start_time;
      pro.new_maintain_stop_time.value = info.maintain_stop_time;
      pro.original_maintain_stop_time.value = info.maintain_stop_time;
      if (flag === "02") {
        this.openCustomerBrand({
          customer_name: info.customer_name,
        });
        return;
      } else if (flag === "01") {
        return;
      } else if ((await this.checkDealCustomer(info.customer_id)) === 0) {
        //判断是不是第一次下单
        this.first_contract = true;
        let configuration = await API.getFirstContractConfiguration();
        if (configuration) {
          pro.detail_sell_type.value =
            configuration.data.detail_sell_type || "";
          pro.contract_amount.value = configuration.data.contract_amount || "";

          pro.contract_price.value = configuration.data.contract_amount || "";
          pro.contract_count.value = configuration.data.contract_count || "";
          pro.measurement_unit.value = this.checkMeasurementUnit(
            configuration.data.detail_sell_type
          );
          pro.fk_brand_id.value = configuration.data.fk_brand_id || "";

          pro.invoice_tax_rate.value = configuration.data.invoice_tax_rate + "";
        }

        if (
          Number(pro.year_maintain_cost.value) > 0 &&
          Number(pro.contract_amount.value) > 0
        ) {
          pro.maintenance_fee.value =
            (pro.contract_amount.value * pro.year_maintain_cost.value) / 100;
        } else {
          pro.maintenance_fee.value = 0;
        }

        let d = new Date();
        pro.maintain_start_time.value = dateFormat("yyyy-MM-dd", d);
        d.setFullYear(d.getFullYear() + 1);
        pro.new_maintain_stop_time.value = dateFormat(
          "yyyy-MM-dd",
          new Date(d.getTime() ) // 去掉少一天 - 24 * 60 * 60 * 1000
        );

        // 审核拒绝之后，替换客户重新提交，不增加合同明细
        if (this.ruleForm.audit_state !== 2) {
          this.$refs.proTableCustom.add2(pro);
        }

        return;
      }
      // else if (this.TmpSellType && this.TmpSellType !== -1) {
      //   //直接从租用导入
      //   console.log('直接从租用导入');
      //   this.sellTypeChange(this.TmpSellType)

      //   return
      // }
      // else if (this.customerBrandFlag) {
      //   //打开客户产品列表
      //   this.$refs.customerBrandList.Show();
      //   this.customerBrandFlag = false;
      //   return;
      // }
      else {
        //点击客户选择，做过合同的
        let customerBrand = await API.getCustomerBrandByCustomer({
          customer_id: this.ruleForm.fk_customer_id,
          brand_ids: data,
          pageSize: 1,
          pageNum: 1,
        });

        if (
          customerBrand &&
          customerBrand.data &&
          customerBrand.data.length === 1
        ) {
          if (sellType) {
            customerBrand.data[0].detail_sell_type = sellType;
          }

          this.add(customerBrand.data[0], "05", sellType);
        }

        // this.customerBrandFlag = false;
      }
    },
    async checkDealCustomer(customer_id) {
      let { data } = await API.checkDealCustomer({
        customer_id: customer_id,
      });
      return data;
    },
    //选择销货单位
    chooseCompany() {
      this.$refs.salesUnitList.Show();
    },
    getCampanyInfo(info = null) {
      if (info) {
        this.ruleForm.sales_unit_id = info.sales_unit_id;
        this.ruleForm.sales_unit_id_format = info.company_name;
      } else {
        this.ruleForm.sales_unit_id = 3;
        this.ruleForm.sales_unit_id_format = "福州吉勤信息科技有限公司";
      }
    },
    //选择介绍合同
    chooseContract() {
      this.$refs.contractList.Show();
    },
    getContractInfo(info = {}) {
      this.ruleForm.introducer_contract_format = info.contract_no;
      this.ruleForm.introducer_contract_id = info.customer_contract_id;
    },
    //选择续费合同
    chooseRenewContract() {
      this.$refs.renewContractList.Show();
    },
    getRenewContractInfo(info = {}) {
      this.ruleForm.renewal_contract_format = info.contract_no;
      this.ruleForm.renewal_contract_id = info.customer_contract_id;
      this.ruleForm.customer_name = info.customer_name;
      this.ruleForm.fk_customer_id = info.fk_customer_id;
      this.ruleForm.fk_sell_employee_id = info.fk_sell_employee_id;
      this.ruleForm.fk_sell_department_id = info.fk_sell_department_id;
      this.ruleForm.fk_sell_employee_name = info.fk_sell_employee_name;
      this.ruleForm.fk_sell_department_name = info.fk_sell_department_name;
      this.ruleForm.link_man = info.link_man;
      this.ruleForm.address = info.address;
      this.ruleForm.fax = info.fax;
      this.ruleForm.province = info.province;
      // if (info.province) {
      //   this.getCityList();
      // }
      this.ruleForm.city = info.city;
      this.ruleForm.link_qq = info.link_qq;
      this.ruleForm.phone = info.phone;
      this.ruleForm.introducer_format = info.introducer_format;
      this.ruleForm.introducer = info.introducer;
      this.ruleForm.introducer_contract_format =
        info.introducer_contract_format;
      this.ruleForm.introducer_contract_id = info.introducer_contract_id;
      this.ruleForm.sales_unit_id = info.sales_unit_id;
      this.ruleForm.sales_unit_id_format = info.sales_unit_id_format;
      this.ruleForm.sell_type = info.sell_type;
      this.ruleForm.pay_type = info.pay_type ? info.pay_type : 1;
      this.ruleForm.train_type = info.train_type ? info.train_type : 2;
      this.ruleForm.remark = info.remark;
      this.ruleForm.fk_recommend_employee_id = info.fk_recommend_employee_id;

      this.$refs.proTableCustom.clear();
      // this.$refs.moduleTableCustom.clear();
      API.detailList({
        customer_contract_id: info.customer_contract_id,
      })
        .then((res) => {
          res.data.map((item) => {
            let pro = JSON.parse(JSON.stringify(this.proObj));
            let module = JSON.parse(JSON.stringify(this.moduleObj));
            for (let v in item) {
              if (item.detail_type == 1) {
                if (pro[v]) {
                  pro[v].value = item[v];
                }
              } else {
                if (module[v]) {
                  module[v].value = item[v];
                }
              }
            }
            if (item.detail_type == 1 && item.new_maintain_stop_time) {
              pro.maintain_start_time.value = item.new_maintain_stop_time;
              let d = new Date(item.new_maintain_stop_time);
              d.setFullYear(d.getFullYear() + 1);
              pro.new_maintain_stop_time.value = dateFormat(
                "yyyy-MM-dd",
                new Date(d.getTime() ) // 去掉少一天 - 24 * 60 * 60 * 1000
              );
              pro.original_maintain_stop_time.value =
                item.new_maintain_stop_time;
            }
            if (item.detail_type == 1) {
              this.$refs.proTableCustom.add2(pro);
            }
            // else {
            //   this.$refs.moduleTableCustom.add2(module);
            // }
          });
        })
        .catch(() => {})
        .finally(() => {});
    },
    //调入报价单
    // callIn() {
    //   this.$refs.quotationList.Show();
    // },
    getQuotationInfo(info = {}) {
      this.ruleForm.customer_name = info.customer_name;
      this.ruleForm.fk_customer_id = info.fk_customer_id;
      this.ruleForm.train_type = info.train_type ? info.train_type : 2;
      this.ruleForm.pay_type = info.pay_type ? info.pay_type : 1;
      this.ruleForm.sell_type = info.sell_type;
      this.ruleForm.address = info.address;
      this.ruleForm.sales_unit_id = info.sales_unit_id;
      this.ruleForm.sales_unit_id_format = info.sales_unit_id_format;
      this.ruleForm.link_man = info.link_man;
      this.ruleForm.introducer = info.introducer;
      this.ruleForm.introducer_format = info.introducer_name;
      this.ruleForm.introducer_contract_id = info.introducer_contract_id;
      this.ruleForm.fk_sell_employee_id = info.fk_sell_employee_id;
      this.ruleForm.introducer_contract_format =
        info.introducer_contract_id_format;
      this.ruleForm.fax = info.fax;
      this.ruleForm.phone = info.phone;
      this.ruleForm.link_qq = info.link_qq;
      this.ruleForm.software_no = info.software_no;
      this.ruleForm.province = info.province;
      this.ruleForm.city = info.city;
      this.ruleForm.fk_sell_department_id = info.department_id;
      this.ruleForm.fk_sell_employee_name = info.fk_sell_employee_name;
      this.ruleForm.fk_sell_department_name = info.fk_sell_department_name;
      // this.getCityList();
      quoAPI
        .detailList({
          quotation_id: info.quotation_id,
        })
        .then((res) => {
          res.data.map((item) => {
            let pro = JSON.parse(JSON.stringify(this.proObj));
            let module = JSON.parse(JSON.stringify(this.moduleObj));
            if (item.detail_type == 1) {
              pro.quotation_id.value = info.quotation_id;
              pro.fk_brand_id.value = item.brand_id;
              // pro.software_version.value = item.software_version;
              pro.measurement_unit.value = item.measurement_unit;

              pro.contract_price.value = item.contract_price;
              pro.contract_count.value = item.quotation_number;
              pro.contract_amount.value = item.quotation_unit;
              pro.invoice_tax_rate.value = item.quotation_rate;
              pro.year_maintain_cost.value = item.years_maintenance_fee;
              // pro.prepayment_rate.value = item.prepayment_ratio;
              pro.maintain_start_time.value = item.maintain_start_time;
              pro.new_maintain_stop_time.value = item.new_maintain_stop_time;
              pro.original_maintain_stop_time.value =
                item.original_maintain_stop_time;
              pro.original_port_count.value = item.qriginal_port_number;
              pro.add_port_count.value = item.new_port_number;
              pro.remark.value = item.remark;
              this.$refs.proTableCustom.add2(pro);
            } else {
              // module.quotation_id.value = info.quotation_id;
              // module.fk_brand_id.value = item.brand_id;
              // module.measurement_unit.value = item.measurement_unit;
              // module.contract_amount.value = item.quotation_unit;
              // module.remark.value = item.remark;
              // this.$refs.moduleTableCustom.add2(module);
            }
          });
        })
        .catch(() => {})
        .finally(() => {});
    },
    //获取省列表
    // getProvinceList() {
    // comAPI
    //   .queryAreaCode({
    //     level: 1,
    //   })
    //   .then((data) => {
    //     this.provinceList = data.data;
    //   })
    //   .catch(() => {});
    // },
    //更换省
    // changeProvince() {
    //   this.cityList = [];
    //   this.ruleForm.city = "";
    //   this.getCityList();
    // },
    //获取市列表
    // getCityList() {
    //   if (!this.ruleForm.province) return;
    //   comAPI
    //     .queryAreaCode({
    //       level: 2,
    //       province: this.ruleForm.province,
    //     })
    //     .then((data) => {
    //       this.cityList = data.data;
    //     })
    //     .catch(() => {});
    // },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        customer_name: "",
        fk_customer_id: "",
        train_type: 2,
        pay_type: 1,
        sell_type: "",
        sales_unit_id: "",
        link_man: "",
        fk_sell_employee_id: "",
        fk_sell_department_id: "",
        phone: "",
        introducer_format: "",
        introducer: "",
        province: "",
        city: "",
        remark: "",
        address: "",
        fax: "",
        software_no: "",
        link_qq: "",
        sales_unit_id_format: "",
        introducer_contract_format: "",
        introducer_contract_id: "",
        fk_recommend_employee_id: "",
      };
    },
    // 销售类型下拉改变
    // sellTypeChange(val) {
    //   this.proObjRest()
    //   const item = this.sell_type.find(item => item.id === val)
    //   for (let key in item) {
    //     let value = item[key]
    //     if (key.includes('_value') && item[key]) {
    //       let itemKey = key.replace('_value', '')
    //       if (this.proObj.hasOwnProperty(itemKey)) {
    //         this.proObj[itemKey].value = value
    //       }
    //     }
    //   }

    //   // if(this.isAdd ){
    //   if ([3, 5].includes(val)) {
    //     //清空产品列表
    //     // this.$refs.proTableCustom.empty()
    //   }
    //   //软件服务费
    //   if (val === 3) {
    //     //统计维护费
    //     if (this.ruleForm.fk_customer_id) {
    //       API.getAllMaintainCost({
    //         fk_customer_id: this.ruleForm.fk_customer_id
    //       }).then(({
    //         data
    //       }) => {
    //         // console.log(data.proObj && data.proObj['contract_detail_id']);
    //         if (data.proObj && data.proObj['contract_detail_id']) {
    //           this.add(data)
    //         }
    //       })
    //     }
    //   }
    //   //软件租用
    //   if (val === 5) {
    //     // 查找该客户所有的租用合同
    //     if (this.ruleForm.fk_customer_id) {
    //       API.getAllRent({
    //         fk_customer_id: this.ruleForm.fk_customer_id
    //       }).then(({
    //         data
    //       }) => {
    //         for (let i = 0; i < data.length; i++) {
    //           this.add(data[i])
    //         }
    //         this.TmpSellType = 0
    //       })
    //     }
    //   }
    //   //从所有客户列表进入，只要查出他的主产品
    //   if (val === 0) {
    //     API.getMainProduct({
    //         fk_customer_id: this.ruleForm.fk_customer_id,
    //       }).then(({
    //         data
    //       }) => {
    //         if (data) {
    //           this.ruleForm.sel_type = 7
    //           this.add(data, '03')
    //         }

    //       })
    //       .catch(() => {});
    //   }
    //   //增加端口 软件升级 二次开发 二次销售 赠送端口
    //   if ([4, 6, 2, 7, 8].includes(val)) {
    //     // 查找该客户所有的租用合同
    //     if (this.ruleForm.fk_customer_id) {
    //       API.getAllRent({
    //         fk_customer_id: this.ruleForm.fk_customer_id
    //       }).then(({
    //         data
    //       }) => {

    //       })
    //     }
    //   }

    //   // }
    // },
    // visibleFunction(sell_type = null) {
    //   // this.ruleForm.sell_type = sell_type
    //   this.TmpSellType = sell_type
    //   this.chooseCustomer(sell_type)
    //   this.visible = false
    // },

    openCustomerBrand(params) {
      if (this.ruleForm.data_state === 2) {
        return this.error("该记录是旧数据，请上旧系统继续操作");
      }
      this.$refs.customerBrandList.Show(params);
      // if (flag) {
      //   this.$refs.customerBrandList.Show()
      // } else {
      //   this.customerBrandFlag = true
      //   this.chooseCustomer()
      //   this.visible = false
      // }
    },
    // 重置行数据
    proObjRest() {
      this.proObj = {
        quotation_id: {
          value: "",
          type: "input",
        },
        fk_brand_id: {
          value: "",
          type: "select",
          option: this.proList,
        },

        detail_sell_type: {
          value: "",
          type: "select",
          option: this.sell_type,
        },

        measurement_unit: {
          value: "",
          type: "select",
          option: this.measurement_unit,
        },
        contract_count: {
          value: "1",
          type: "float",
          fn: "f3",
        },
        contract_price: {
          value: "",
          type: "float",
          isNeed: true,
        },
        contract_amount: {
          value: "",
          type: "float",
          isNeed: true,
        },
        invoice_tax_rate: {
          value: "",
          type: "select",
          option: this.quotationRateList,
        },
        // prepayment_rate: {
        //   value: "100",
        //   type: "select",
        //   option: this.prepaymentRatioList,
        // },
        year_maintain_cost: {
          value: "100",
          type: "select",
          option: this.yearsFeeList,
        },

        maintenance_fee: {
          value: "",
          type: "input",
        },
        // prepayment_time_limit: {
        //   value: "",
        //   type: "date",
        // },
        maintain_start_time: {
          value: "",
          type: "date",
        },
        new_maintain_stop_time: {
          value: "",
          type: "date",
        },
        original_maintain_stop_time: {
          value: "",
          type: "date",
          disabled: true,
        },
        //原有端口数
        original_port_count: {
          value: "",
          type: "number",
          disabled: true,
        },
        add_port_count: {
          value: 0,
          type: "number",
        },
        remark: {
          value: "",
          type: "input",
        },
        software_no: {
          value: "",
          type: "number",
          disabled: true,
        },
      };
    },
    /**
     * 根据销售类型，判断计量单位
     */
    checkMeasurementUnit(value) {

      if (!value) return null;
      //
      if ([1].includes(value)) {
        return 2;
      } else if ([3, 5].includes(value)) {
        return 4;
      } else if ([12].includes(value)) {
        return 3;
      } else if ([4,8].includes(value)) {
        return 1;
      } else if ([13].includes(value)) {
        return 5;
      } else {
        return 2;
      }
    },
    /**
     * 计算年维护费
     */
    checkMaintenanceFee(row) {
      // let money = 0
      // if (
      //   Number(row.contract_amount.value) &&
      //   Number(row.year_maintain_cost.value)
      // ) {
      //   money =  (
      //     (Number(row.contract_amount.value) *
      //       Number(row.year_maintain_cost.value)) /
      //     100
      //   )
      // }

      // if (Number(money) > 0 && Number(money)  < 500) {
      //   return Number(500).toFixed(2);
      // }
      // return Number(money).toFixed(2);
      //如果是软件销售/软件租用

      return checkMaintenanceFee(row);
    },
    //不做处理
    clickFileCount() {},
    dialogCancelAudit() {
      this.dialogVisibleAudit = false;
      this.resetForm("auditForm");
    },
    getTotalMoney(money){
      this.total_money = money
  }
  },
  computed: {
    ...mapGetters([
      "userInfo",
      "buttonPermissions",
      "contract_train_type",
      "measurement_unit",
      "sell_type",
      "contract_pay_type",
      "contract_auditStateList"
      // "software_version",
    ]),
    addUserInfo() {
      let name = "";

      if (
        this.ruleForm.add_user_name &&
        this.ruleForm.fk_operator_department_name
      ) {
        name =
          this.ruleForm.add_user_name +
          "-" +
          this.ruleForm.fk_operator_department_name;
      }

      return name;
    },
    fkSaleEmployeeUserInfo() {
      let name = "";

      if (
        this.ruleForm.fk_sell_employee_name &&
        this.ruleForm.fk_sell_department_name
      ) {
        name =
          this.ruleForm.fk_sell_employee_name +
            "-" +
            this.ruleForm.fk_sell_department_name || "";
      }

      return name;
    },
  
  },
  watch: {
    "ruleForm.fk_customer_id": {
      handler(newValue, oldValue) {
        if (this.ruleForm.port_number && this.ruleForm.port_number > 0) {
          this.$refs.proTableCustom.putCustomerPort({
            port_number: this.ruleForm.port_number,
          });
        }
        if (this.ruleForm.fk_customer_id) {
          this.$refs.proTableCustom.putBrandSoftwareNo({
            fk_customer_id: this.ruleForm.fk_customer_id,
          });
        }
      },
      immediate: true,
      deep: true,
    },
  },
};
