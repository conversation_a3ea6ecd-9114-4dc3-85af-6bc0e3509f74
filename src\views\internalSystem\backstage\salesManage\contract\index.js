import API from "@/api/internalSystem/salesManage/contract";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import AddContract from "./components/addContract/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import AuditDetail from "@/mixins/auditDetail.js";
import brandAPI from "@/api/internalSystem/basicManage/brand";
import salesUnitAPI from '@/api/internalSystem/basicManage/salesUnit'
import { mapGetters } from "vuex";
import moment from "moment";
export default {
  name: "contract",
  mixins: [AuditDetail],
  data() {
    return {
      title: "销售合同单",
      loading: false,
      tableData: [],
      formSearch: {
        customer_name: "",
        fk_sell_employee_id: "",
        add_user_id: "",
        sell_type: "",
        brand_id: "",
        audit_state: "",
        startTime: "",
        endTime: "",
        maintain_start_time: "",
        maintain_stop_time: "",
        has_receivables: "",
        not_has_receivables: "",
        has_open_ticket: "",
        not_has_open_ticket: "",
        has_outbound: "",
        not_has_outbound: "",
        has_contract_anexo: "",
        not_has_contract_anexo: "",
        invoice_tax_rate_gt_zero:"",
        invoice_tax_rate_eq_zero:"",
      },
      tableList: [
        // {
        //   name: "完成情况",
        //   value: "state_name",
        //   width: 70,
        // },
        {
          name: "审核状态",
          value: "audit_state_name",
          width: 82,
        },
        {
          name: "单据编号",
          value: "contract_no",
          width: 116,
        },
        {
          name: "客户编号",
          value: "customer_no",
          width: 70,
        },
        // {
        //   name: "合同数量",
        //   value: "contract_count",
        //   width: 116,
        // },
        {
          name: "客户名称",
          value: "customer_name",
          width: 200,
        },

        {
          name: "合同金额",
          value: "contract_amount",
          width: 72,
        },
        {
          name: "增加端口",
          value: "add_port",
        },
        {
          name: "软件序列号",
          value: "software_no",
          width: 100,
        },

        // {
        //   name: "维护起始时间",
        //   value: "maintain_start_time",
        //   width: 96,
        // },
        {
          name: "维护结束时间",
          value: "maintain_stop_time",
          width: 96,
        },
        // {
        //   name: "剩余维护天数",
        //   value: "balance_days",
        //   width: 120,
        //   isSlot: true,
        //   isHide: true,
        //   // sortable:'custom'
        // },

        {
          name: "已收款金额",
          value: "receivables_amount",
          width: 82,
        },
        // {
        //   name: "未收款金额",
        //   value: "not_receivables_amount",
        //   width: 82,
        // },
        {
          name: "已开票金额",
          value: "open_ticket_amount",
          width: 82,
        },
        // {
        //   name: "是否出库",
        //   value: "outbound",
        //   width: 82,
        // },
        {
          name: "附件情况",
          value: "contract_anexo_status",
          width: 82,
        },
        {
          name: "销售类型",
          value: "detail_sell_type_name",
          width: 90,
        },
        {
          name: "销售员",
          value: "fk_sell_employee_name",
          width: 70,
        },
        // {
        //   name: "未开票金额",
        //   value: "not_open_ticket_amount",
        //   width: 82,
        // },
        {
          name: "单据备注",
          value: "remark",
          width: 70,
        },
        {
          name: "付款方式",
          value: "pay_type_name",
          width: 106,
        },
        {
          name: "培训方式",
          value: "train_type_name",
          width: 70,
        },
        // {
        //   name: "销售类型",
        //   value: "sell_type_name",
        //   width: 82,
        // },
        // {
        //   name: "推荐员工",
        //   value: "fk_recommend_employee_name",
        //   width: 70,
        // },
        {
          name: "操作员工",
          value: "add_user_name",
          width: 70,
        },
        {
          name: "操作部门",
          value: "fk_operator_department_name",
          width: 70,
        },
        // {
        //   name: "手机",
        //   value: "phone",
        //   width: 100,
        // },
        // {
        //   name: "客户传真",
        //   value: "fax",
        //   width: 100,
        // },
        {
          name: "联系人",
          value: "link_man",
          width: 70,
        },
        // {
        //   name: "介绍人",
        //   value: "introducer_format",
        // },
        // {
        //   name: "介绍合同",
        //   value: "introducer_contract_format",
        // },

        {
          name: "销售部门",
          value: "fk_sell_department_name",
          width: 70,
        },
        {
          name: "单据日期",
          value: "update_time",
          width: 90,
        },
        // {
        //   name: "客户地址",
        //   value: "address",
        // },
        // {
        //   name: "联系人QQ",
        //   value: "link_qq",
        //   width: 110,
        // },
      ],
      employeeList: [],
      isAdd: false,
      allList: {
        contract_amount: 0,
        receivables_amount: 0,
        open_ticket_amount: 0,
        not_receivables_amount: 0,
        not_open_ticket_amount: 0,
      },
      balance_days_list: [
        {
          label: "30-60天",
          value: "01",
        },
        {
          label: "7-30天",
          value: "02",
        },
        {
          label: "0-7天",
          value: "03",
        },
      ],
      brandList: [],
      salesUnits: [],
      dialogVisible: false,
      excelData:[]
    };
  },
  created() {
    if (!["总经理"].includes(this.cookiesUserInfo.role_name)) {
      this.formSearch.fk_sell_employee_id = this.cookiesUserInfo.userId;
    }
  },
  activated() {
    this.timingQuery()
    if (this.$route.query.row) {
      this.isAdd = true;
      this.$refs.addContract.xufei(
        this.$route.query.row,
        this.$route.query.sellType
      );
    }
    let params = this.$route.params;
    if (params.type === "home") {
      Object.assign(this.formSearch, params);
      this.$refs.addContract.dialogCancel(true, false);
    }

    if (this.$route.query.type === "authorization") {
      this.getInfo(
        this.$route.query.customer_contract_id,
        this.$route.query.type
      );
    }
  },
  mounted() {

    if (!this.$route.params.type) this.getList();
    this.getBrandList();
    this.getSalesUnit();
    this.$store.dispatch("getEmployee").then((res) => {
      this.employeeList = res;
    });
  },
  methods: {
    timingQuery(){
      // setInterval(() => {
        API.queryToAudit().then(res=>{
          if(res && res.data > 0){
            this.$notify({
              title: '警告',
              message: `有 ${res.data} 条待发起审核合同，请尽快处理`,
              type: 'warning',
              duration: 5000,
              position: 'bottom-right'

            });
          }
        })
      // }, 1000 * 60);
 
    },
    openFullScreen() {
      let loading = this.$loading({
        lock: false,
        text: "加载中...",
        background: "rgba(0, 0, 0, 0.7)",
        spinner: "el-icon-loading",
      });
      return loading;
    },
    closeFullScreen(loading) {
      loading.close();
    },
    dialogVisibleQuery() {
      this.dialogVisible = false;
      this.getList(true);
    },
    dialogVisibleClick() {
      this.dialogVisible = true;
    },
    getBrandList() {
      brandAPI["query2"]().then((data) => {
        data.data.forEach((item) => {
          // if (item.brand_classify == 1)
          this.brandList.push({
            label: item.brand_type,
            value: item.brand_id,
          });
        });
      });
    },
    getSalesUnit() {
      salesUnitAPI["query"]().then((data) => {
        this.salesUnits = data.data
      });
    },
    getList(f = false) {
      this.isAdd = false;
      this.isAudit = false;
      this.loading = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          let param = Object.assign(
            this.formSearch,
            this.$refs.pagination.obtain()
          );
          if (f) param.pageNum = 1;
          param.startTime = param.startTime ? param.startTime : "";
          param.endTime = param.endTime ? param.endTime : "";
          if (param.has_receivables && param.not_has_receivables) {
            return this.error("已收款和未收款冲突了");
          }
          if (param.not_has_open_ticket && param.has_outbound) {
            return this.error("已开票和未开票冲突了");
          }
          // if (param.has_outbound && param.not_has_outbound) {
          //   return this.error("已出库和未出库突了");
          // }
          if (param.has_contract_anexo && param.not_has_contract_anexo) {
            return this.error("已上传附件和未上传附件突了");
          }
          if (param.invoice_tax_rate_eq_zero && param.invoice_tax_rate_gt_zero) {
            return this.error("发票税率>0和发票税率=0冲突");
          }
          param.isJurisdiction = this.permissionToCheck("all") ? 1 : 0;
          this.openFullScreen();
          API.query(param).then((res) => {
              this.tableData = res.data;
              this.$refs.pagination.setTotal(res.totalCount);
              this.closeFullScreen(this.openFullScreen());
            })
            .finally(() => {
              this.loading = false;
            });
          API.queryAll(param).then((res) => {
              if (res.data.length) this.allList = res.data[0];
          });
 
        });
      // }, 300);
    },
    add() {
      this.isAdd = true;
      this.$refs.addContract.Show();
    },
    getInfo(customer_contract_id, type = null) {
      let params = {
        customer_contract_id: customer_contract_id,
      };
      API.getInfo(params)
        .then((data) => {
          this.isAdd = true;
          // this.$refs.addContract.dialogCancel(false)
          this.$refs.addContract.Show(data.data, type);
        })
        .catch(() => {});
    },
    modify(item) {
      this.getInfo(item.customer_contract_id);
    },
    del(item) {
      if (item.data_state === 2) {
        return this.error("该记录是旧数据，请上旧系统继续操作");
      }
      // if(item && item.data_state ===2){
      //   return this.error('该记录是旧数据，请上旧系统继续操作')
      // }

      if (item.audit_state == 1 || item.audit_state == 0)
        return this.error("该单据已发出审核，不允许删除");
      let params = {
        customer_contract_id: item.customer_contract_id,
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
    rowDblclick(row, column, event) {
      this.modify(row);
    },
    handleExcel() {
      this.$confirm("是否导出数据到excel文件?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const expLoading = this.$loading({
            lock: true,
            text: "正在导出数据，请稍候...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          let param = Object.assign(
            this.formSearch,
            this.$refs.pagination.obtain({newSize:50})
          );
          // param.pageNum = 1
          // param.pageSize = 999999
          const { data } = await API.query(param);
          this.excelData = data;
           this.export2Excel(expLoading);
        })
        .catch(() => {});
    },
    export2Excel(expLoading) {
      const that = this;
      const option = {};
      option.tHeader = [
        "审核状态",
        "单据编号",
        "客户名称",
        "合同金额",
        "增加端口",
        "软件序列号",
        "维护结束时间",
        "已收款金额",
        "已开票金额",
        // "是否出库",
        "附件情况",
        "销售类型",
        "销售员",
        "单据备注",
        "付款方式",
        "培训方式",
        "操作员工",
        "操作部门",
        "联系人",
        "销售部门",
        "单据日期",
      ];
      option.filterVal = [
        "audit_state_name",
        "contract_no",
        "customer_name",
        "contract_amount",
        "add_port",
        "software_no",
        "maintain_stop_time",
        "receivables_amount",
        "open_ticket_amount",
        // "outbound",
        "contract_anexo_status",
        "detail_sell_type_name",
        "fk_sell_employee_name",
        "remark",
        "pay_type_name",
        "train_type_name",
        "add_user_name",
        "fk_operator_department_name",
        "link_man",
        "fk_sell_department_name",
        "update_time",
      ];
      option.name = "销售合同导出";
      require.ensure([], () => {
        const { export_json_to_excel } = require("@/utils/Export2Excel"); // 这里必须使用绝对路径
        const tHeader = option.tHeader; // 导出的表头名
        const filterVal = option.filterVal; // 导出的表头字段名
        const list = that.excelData;
        const data = that.formatJson(filterVal, list);
        expLoading.close();
        export_json_to_excel(tHeader, data, option.name); // 导出的表格名称，根据需要自己命名
      });
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) =>
        filterVal.map((j) => {
          return v[j];
        })
      );
    },
  },

  components: {
    AddContract,
    Pagination,
    TableView,
    MyDate,
  },
  computed: {
    ...mapGetters(["userInfo","sell_type", "customer_contract_audit","cookiesUserInfo"]),
  },
};
