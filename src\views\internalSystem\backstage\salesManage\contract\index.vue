<template>
  <div class="body-p10">
    <el-form
      :inline="true"
      :model="formSearch"
      size="small"
      v-if="!isAdd && !isAudit"
      class="formSearch-bottom"
    >
      <el-form-item label="查询条件">
        <el-input
          v-model="formSearch.customer_name"
          placeholder="请输入客户名称"
          clearable
          style="width: 220px"
        ></el-input>
      </el-form-item>
      <!-- <el-form-item>
        <el-select
          v-model="formSearch.fk_sell_employee_id"
          placeholder="请选择销售员"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="item in employeeList"
            :key="item.employeeId"
            :label="item.employee_number + '-' + item.employee_name"
            :value="item.employeeId"
          ></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-select
          v-model="formSearch.sales_unit_id"
          placeholder="请选择销货单位"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="item in salesUnits"
            :key="item.sales_unit_id"
            :label="item.company_name"
            :value="item.sales_unit_id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.sell_type"
          placeholder="请选择销售类型"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="item in sell_type"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.fk_sell_employee_id"
          placeholder="请选择销售员"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
          :disabled="!['总经理'].includes(cookiesUserInfo.role_name)"
        >
          <el-option
            v-for="item in employeeList"
            :key="item.employeeId"
            :label="item.employee_number + '-' + item.employee_name"
            :value="item.employeeId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.audit_state"
          placeholder="请选择审核状态"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="item in customer_contract_audit"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <my-date
          v-model="formSearch.startTime"
          hint="开始时间"
          style="width: 130px"
        ></my-date>
      </el-form-item>
      <el-form-item>
        <my-date
          v-model="formSearch.endTime"
          hint="结束时间"
          style="width: 130px"
        ></my-date>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading"
          >查询</el-button
        >
        <el-button type="primary" @click="dialogVisibleClick()"
          >过滤条件</el-button
        >
        <el-button type="primary" @click="add" v-permit="'ADD_CONTRACT_NEW'"
          >制单</el-button
        >
        <el-button type="primary" @click="handleExcel" 
          >导出</el-button
        >
      </el-form-item>
    </el-form>
    <TableView
      :tableList="tableList"
      :tableData="tableData"
      v-if="!isAdd && !isAudit"
      @del="del"
      @modify="modify"
      :isDblclick="true"
      @rowDblclick="rowDblclick"
      isEdit="permanent_button"
      :isDel="'DEL_CONTRACT_NEW'"
      isThrid="AUDIT_CONTRACT_NEW"
      :thridTitle="'审核'"
      @thrid="(item) => toAuditDet(item, '合同单审核', 'audit_state')"
      isFour="permanent_button"
      :fourTitle="'附件'"
      @four="openFileList"
    ></TableView>
    <div class="mt10 moneyTitle" v-if="!isAdd && !isAudit">
      <el-row>
        <el-col :span="4">合同总金额：{{ allList.contract_amount }}元</el-col>
        <el-col :span="4"
          >已收款总金额：{{ allList.receivables_amount }}元</el-col
        >
        <el-col :span="4"
          >未收款总金额：{{ allList.not_receivables_amount }}元</el-col
        >
        <el-col :span="4"
          >已开票总金额：{{ allList.open_ticket_amount }}元</el-col
        >
        <el-col :span="4"
          >未开票总金额：{{ allList.not_open_ticket_amount }}元</el-col
        >
      </el-row>
    </div>
    <Pagination
      ref="pagination"

      @success="getList"
      v-show="!isAdd && !isAudit"
    />
    <!-- 新增销售合同单 -->
    <AddContract ref="addContract" @getInfo="getInfo" @selectData="getList" />
    <!-- 审核 -->
    <ContractAudit ref="contractAudit" v-show="isAudit" @selectData="getList" />
    <el-dialog
      title="查询条件"
      :visible.sync="dialogVisible"
      width="38%"
      v-dialogDrag
    >
      <el-form ref="form" :model="formSearch" label-width="130px">
        <el-form-item label="客户名称：">
          <el-input
            v-model="formSearch.customer_name"
            placeholder="请输入客户名称"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item label="销售类型：">
          <el-select
            v-model="formSearch.sell_type"
            placeholder="请选择销售类型"
            class="inputBox"
            filterable
            clearable
          >
            <el-option
              v-for="item in sell_type"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="销售员：">
          <el-select
            v-model="formSearch.fk_sell_employee_id"
            placeholder="请选择销售员"
            class="inputBox"
            filterable
            clearable
            :disabled="!['总经理'].includes(userInfo.role_name)"
          >
            <el-option
              v-for="item in employeeList"
              :key="item.employeeId"
              :label="item.employee_number + '-' + item.employee_name"
              :value="item.employeeId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="制单员：">
          <el-select
            v-model="formSearch.add_user_id"
            placeholder="请选择制单员"
            class="inputBox"
            filterable
            clearable
          >
            <el-option
              v-for="item in employeeList"
              :key="item.employeeId"
              :label="item.employee_number + '-' + item.employee_name"
              :value="item.employeeId"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="产品：">
          <el-select
            v-model="formSearch.brand_id"
            placeholder="请选择产品"
            class="inputBox"
            filterable
            clearable
          >
            <el-option
              v-for="item in brandList"
              :key="item.id"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="开始时间：">
          <my-date
            v-model="formSearch.startTime"
            hint="请选择开始时间"
          ></my-date>
        </el-form-item>
        <el-form-item label="结束时间：">
          <my-date v-model="formSearch.endTime" hint="请选择结束时间"></my-date>
        </el-form-item>
        <el-form-item label="维护费开始时间：">
          <my-date
            v-model="formSearch.maintain_start_time"
            hint="请选择维护费开始时间"
          ></my-date>
        </el-form-item>
        <el-form-item label="维护费结束时间：">
          <my-date
            v-model="formSearch.maintain_stop_time"
            hint="请选择维护费结束时间"
          ></my-date>
        </el-form-item>
        <!-- <el-form-item label="剩余维护天数：" >
          <el-select
            v-model="formSearch.balance_days"
            placeholder="请选择剩余维护天数"
            class="inputBox"
            clearable
          >
            <el-option
              v-for="(item, index) in balance_days_list"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-checkbox v-model="formSearch.has_receivables">已收全款</el-checkbox>
          <el-checkbox v-model="formSearch.has_open_ticket">已开票</el-checkbox>
          <!-- <el-checkbox v-model="formSearch.has_outbound">已出库</el-checkbox> -->
          <el-checkbox v-model="formSearch.has_contract_anexo"
            >已上传附件</el-checkbox
          >
          <el-checkbox v-model="formSearch.invoice_tax_rate_gt_zero"
            >发票税率>0</el-checkbox
          >
          <div></div>
          <el-checkbox v-model="formSearch.not_has_receivables"
            >未收全款</el-checkbox
          >
          <el-checkbox v-model="formSearch.not_has_open_ticket"
            >未开票</el-checkbox
          >
          <!-- <el-checkbox v-model="formSearch.not_has_outbound"
            >未出库</el-checkbox
          > -->
          <el-checkbox v-model="formSearch.not_has_contract_anexo"
            >未上传附件</el-checkbox
          >
          <el-checkbox v-model="formSearch.invoice_tax_rate_eq_zero"
            >发票税率=0</el-checkbox
          >
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="dialogVisibleQuery()"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <!-- 附件列表 -->
    <FileList ref="fileList" />
  </div>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
.moneyTitle {
  font-size: 14px;
  font-weight: bold;
}
@import "@/assets/css/element/font-color.scss";
</style>
