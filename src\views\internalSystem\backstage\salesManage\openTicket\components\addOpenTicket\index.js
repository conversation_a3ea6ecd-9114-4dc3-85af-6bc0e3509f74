import API from "@/api/internalSystem/salesManage/openTicket";
import cusAPI from "@/api/internalSystem/customerManage/customerInfo";
import conAPI from "@/api/internalSystem/salesManage/contract";
import brandAPI from "@/api/internalSystem/basicManage/brand";
import ContractList from "@/views/internalSystem/backstage/components/contractList/index.vue";
import FileList from "@/views/internalSystem/backstage/components/fileList/index.vue";
import TableCustom from "@/views/internalSystem/backstage/components/tableCustom/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import { getOptions } from "@/common/internalSystem/common.js";
import { mapGetters } from "vuex";

export default {
  name: "addOpenTicket",
  components: {
    ContractList,
    FileList,
    TableCustom,
    MyDate,
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        ticket_no: "",
        ticket_date: "",
        ticket_remark: "",
        fk_sale_employee_id: "",
        fk_sale_employee_name: "",
        sell_type: "",
        customer_name: "",
        customer_no: "",
        customer_tax_number: "",
        customer_account: "",
        invoice_number: "",
        express_number: "",
        open_ticket_address: "",
        open_account_bank: "",
        open_ticket_phone: "",
        receive_ticket_address: "",
        receive_ticket_person: "",
        receive_ticket_phone: "",
        fk_sales_unit_id: "",
        sales_unit_id_format: "",
        update_user_name: "",
      },
      softwareVersionList: [], //软件版本
      measurementUnitList: [], //计量单位
      loading: false,
      proList: [], //产品列表
      tableCol: [
        {
          label: "id",
          prop: "contract_id",
          isHide: true,
        },
        {
          label: "合同单号",
          prop: "contract_no",
          width: 160,
        },
        {
          label: "产品",
          prop: "brand_id",
          // width: 160
        },
        // {
        // 	label: "销售类型",
        // 	prop: "detail_sell_type",
        // 	need: true,
        // 	width: 180,
        // },
        {
          label: "开票名称",
          prop: "open_ticket_name",
          // width: 160
        },
        {
          label: "计量单位",
          prop: "measurement_unit",
          width: 160,
        },
        {
          label: "开票单价(元)",
          prop: "open_ticket_unit",
          width: 160,
          need: true,
          fn: "f1",
        },
        {
          label: "未税开票金额(元)",
          prop: "open_ticket_money",
          width: 160,
        },
        {
          label: "发票税率(%)",
          prop: "invoice_tax_rate",
          width: 160,
        },
        {
          label: "发票数量",
          prop: "open_ticket_number",
          width: 160,
          need: true,
          isEditConfig: true,
          fn: "f2",
        },
        // {
        // 	label: "扣税开票单价(元)",
        // 	prop: "buckle_open_ticket_unit",
        // 	width: 160
        // },
        //  {
        // 	label: "税额(元)",
        // 	prop: "tax",
        // 	width: 160
        // },
        //  {
        // 	label: "价税合计(元)",
        // 	prop: "leved_total",
        // 	width: 160
        // },
        // {
        // 	label: "软件版本",
        // 	prop: "software_version",
        // 	width: 160
        // },

        // {
        // 	label: "开票型号",
        // 	prop: "open_ticket_model",
        // 	width: 160
        // },
        {
          label: "软件序列号",
          prop: "software_no",
          width: 160,
        },
      ],
      obj: {},
      fk_customer_contract_ids: [],
      isEdit: false,
      isOwn: true,
      dialogVisibleTem: false,
      temForm: {
        contract_template_id: "",
      },
      temRules: {
        contract_template_id: [
          {
            required: true,
            message: " ",
            trigger: "change",
          },
        ],
      },
      rules: {
        customer_tax_number: [
          {
            required: true,
            message: " ",
            trigger: "change",
          },
        ],
        open_ticket_address: [
          {
            required: true,
            message: " ",
            trigger: "change",
          },
        ],
        open_account_bank: [
          {
            required: true,
            message: " ",
            trigger: "open_account_bank",
          },
        ],
        open_ticket_phone: [
          {
            required: true,
            message: " ",
            trigger: "change",
          },
        ],
      },
      temList: [],
      command: "",
      sell_type_dict: [
        {
          label: "软件销售",
          id: 1,
        },
        {
          label: "二次开发",
          id: 2,
        },
        {
          label: "软件服务费",
          id: 3,
        },
        {
          label: "增加端口",
          id: 4,
        },
        {
          label: "软件租用",
          id: 5,
        },
        {
          label: "软件升级",
          id: 6,
        },
        {
          label: "软件销售",
          id: 7,
        },
        {
          label: "赠送端口",
          id: 8,
        },
        {
          label: "金万维",
          id: 9,
        },
        {
          label: "项目款",
          id: 10,
        },
        {
          label: "网站",
          id: 11,
        },
        {
          label: "硬件销售",
          id: 12,
        },
      ],
      total_money: 0,
      totalOpenMoney: 0,
    };
  },
  methods: {
    async Show(data = null) {
      this.total_money = 0;
this.totalOpenMoney = 0
      this.proList = [];
      this.fk_customer_contract_ids = [];
      this.softwareVersionList = [];
      this.measurementUnitList = [];
      this.dialogVisible = true;
      // this.softwareVersionList = getOptions('t_contract_detail', 'software_version');
      this.measurementUnitList = getOptions(
        "t_contract_detail",
        "measurement_unit"
      );
      this.softwareVersionList.forEach((item) => {
        item.label = item.sysName;
        item.value = item.sysValue;
      });
      this.measurementUnitList.forEach((item) => {
        item.label = item.sysName;
        item.value = item.sysValue;
      });
      this.isEdit = true;
      this.isOwn = true;
      await this.getBrand();
      this.obj = {
        contract_id: {
          value: "",
          type: "input",
        },
        contract_no: {
          value: "",
          type: "input",
          disabled: true,
        },
        brand_id: {
          value: "",
          type: "select",
          option: this.proList,
          disabled: true,
        },
        detail_sell_type: {
          value: "",
          type: "select",
          option: this.sell_type_dict,
          disabled: true,
        },
        open_ticket_name: {
          value: "",
          type: "select",
          option: this.proList,
          disabled: true,
        },
        open_ticket_unit: {
          value: "",
          type: "float",
          disabled: true,
        },
        invoice_tax_rate: {
          value: "",
          type: "input",
          disabled: true,
        },
        open_ticket_number: {
          value: 1,
          type: "number",
        },
        buckle_open_ticket_unit: {
          value: "",
          type: "float",
          disabled: true,
        },
        tax: {
          value: "",
          type: "float",
          disabled: true,
        },
        open_ticket_money: {
          value: "",
          type: "float",
          disabled: true,
        },
        leved_total: {
          value: "",
          type: "float",
          disabled: true,
        },

        measurement_unit: {
          value: "",
          type: "select",
          option: this.measurement_unit,
          disabled: true,
        },

        software_no: {
          value: "",
          type: "input",
          disabled: true,
        },
      };
      if (data) {
        this.ruleForm = data;
        this.fk_customer_contract_ids = data.fk_customer_contract_ids.split(
          ","
        );
        this.isEdit =
          this.permissionToCheck("UPDATE_OPENTICKET_NEW") &&
          (data.audit_state == 4 || data.audit_state == 2);

        this.isOwn = [data.update_user_id, data.fk_sale_employee_id].includes(
          this.userInfo.userId
        )
          ? true
          : false;

        //       this.isOwn =
        //  data.fk_sale_employee_id === this.userInfo.userId ? true : false;
        API.detailList({
          ticket_id: data.ticket_id,
        })
          .then((res) => {
            res.data.map((item) => {
              let pro = JSON.parse(JSON.stringify(this.obj));
              for (let v in item) {
                if (pro[v]) {
                  pro[v].value = item[v];
                  if (v === "open_ticket_number")
                    pro[v].disabled = !this.isEdit || !this.isOwn;
                  else pro[v].disabled = true;
                }
              }
              this.$refs.tableCustom.add2(pro);
            });
          })
          .catch(() => {})
          .finally(() => {});
        this.getTotalOpenMoney(data.contract_id,data.sales_unit_id);
      }
    },
    //提交
    submitForm(formName) {
      if (this.ruleForm.data_state === 2) {
        return this.error("该记录是旧数据，请上旧系统继续操作");
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // if (Number(this.totalOpenMoney) > Number(this.total_money)) {
          //   return this.error(
          //     "可开票总金额 大于 本次开票金额，请检查该客户的其他合同"
          //   );
          // } else {
            this.checkTax();
          // }
        } else {
          return false;
        }
      });
    },
    async checkTax() {
      let proList = [];
      try {
        proList = await this.$refs.tableCustom.getData();
      } catch {
        return;
      }

      if (proList.length == 0) return this.error("至少有一条数据");
      let invoice_tax_rate_flag = false;
      proList.map((item) => {
        if (
          !item.invoice_tax_rate.value ||
          Number(item.invoice_tax_rate.value) === 0
        ) {
          invoice_tax_rate_flag = true;
        }
      });

      if (invoice_tax_rate_flag) {
        this.$confirm("存在开票税率是 0 的明细, 是否继续保存?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.save(proList);
          })
          .catch(() => {});
      } else {
        this.save(proList);
      }
    },
    dialogCancel(flag = true) {
      if (!flag) {
        this.clearData();
      } else {
        this.dialogVisible = false;
        this.clearData();
        this.resetForm("ruleForm");
        this.$emit("selectData");
      }
    },
    fromHomeCancel() {
      this.dialogVisible = false;
      this.$emit("selectData");
    },
    async save(proList) {
      let params = this.ruleForm;
      let detail = [];
      proList.forEach((item) => {
        if (item.contract_id.value) {
          this.fk_customer_contract_ids.push(item.contract_id.value);
        }

        detail.push({
          brand_id: item.brand_id.value,
          contract_no: item.contract_no.value,
          contract_id: item.contract_id.value,
          // software_version: item.software_version.value,
          measurement_unit: item.measurement_unit.value,
          // open_ticket_model: item.open_ticket_model.value,
          open_ticket_name: item.open_ticket_name.value,
          detail_sell_type: item.detail_sell_type.value,

          open_ticket_number: item.open_ticket_number.value,
          open_ticket_unit: item.open_ticket_unit.value,
          buckle_open_ticket_unit: item.buckle_open_ticket_unit.value,
          tax: item.tax.value,
          open_ticket_money: item.open_ticket_money.value,
          leved_total: item.leved_total.value,
          invoice_tax_rate: item.invoice_tax_rate.value,
          software_no: item.software_no.value,
        });
      });
      let ids = Array.from(new Set(this.fk_customer_contract_ids));

      params.fk_customer_contract_ids = ids.join(",");
      params.detail = detail;
      params.audit_state = 4;
      // params.audit_state = '0';
      this.loading = true;
      if (params.ticket_id) {
        if (this.ruleForm.audit_state != 4 && this.ruleForm.audit_state != 2)
          return this.error("该单据已发出审核，不允许修改！");
        API.update(params)
          .then(() => {
            this.$emit("modify", { ticket_id: params.ticket_id });
            this.dialogCancel();
          })
          .catch(() => {})
          .finally(() => {
            this.loading = false;
          });
      } else {
        API.add(params)
          .then((res) => {
            this.$emit("modify", { ticket_id: res.data.insertId });
            this.dialogCancel();
          })
          .catch(() => {})
          .finally(() => {
            this.loading = false;
          });
      }
    },
    send() {
      if (this.ruleForm.data_state === 2) {
        return this.error("该记录是旧数据，请上旧系统继续操作");
      }

      this.$confirm("此操作将发出该条记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            ticket_id: this.ruleForm.ticket_id,
            auditState: 3,
          };
          API.updateAudit(params)
            .then(() => {
              this.dialogCancel();
            })
            .catch(() => {})
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {});
    },
    back() {
      if (this.ruleForm.data_state === 2) {
        return this.error("该记录是旧数据，请上旧系统继续操作");
      }
      this.$confirm("此操作将回退该条记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            ticket_id: this.ruleForm.ticket_id,
            auditState: 3,
          };
          API.updateAudit(params)
            .then(() => {
              this.dialogCancel();
            })
            .catch(() => {})
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {});
    },
    addFile() {
      if (this.ruleForm.data_state === 2) {
        return this.error("该记录是旧数据，请上旧系统继续操作");
      }
      this.$refs.fileList.Show();
    },
    getBrand() {
      return new Promise((resolve, reject) => {
        brandAPI
          .query()
          .then((data) => {
            data.data.forEach((item) => {
              this.proList.push({
                label: item.brand_type,
                value: item.brand_id,
              });
            });
            resolve(1);
          })
          .catch(() => {
            reject();
          });
      });
    },
    handleCommand(command) {
      this.command = command;
      this.dialogVisibleTem = true;
      API.templateList()
        .then((data) => {
          this.temList = data.data;
        })
        .catch(() => {});
    },
    submitTem(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          window.open("/internalSystem/#/print", "_target");
        } else {
          return false;
        }
      });
    },
    dialogCancelTem() {
      this.dialogVisibleTem = false;
    },
    del(info = {}) {
      if (this.ruleForm.data_state === 2) {
        return this.error("该记录是旧数据，请上旧系统继续操作");
      }
      if (info.audit_state == 1 || info.audit_state == 0)
        return this.error("该单据已发出审核，不允许删除");
      let params = {
        ticket_id: info.ticket_id,
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
      // let delIndex = ""
      // this.fk_customer_contract_ids.map((item, index) => {
      // 	if (item === info.contract_id) {
      // 		delIndex = index
      // 	}
      // })
      // this.fk_customer_contract_ids.splice(delIndex, 1);
      // if (this.$refs.tableCustom.getData2().length == 0 || (this.$refs.tableCustom.getData2().length == 1 && this.$refs.tableCustom.getData2()[0] == info)) {
      // 	this.resetForm('ruleForm');
      // 	this.clearData();
      // }
      this.dialogCancel(true);
    },
    //调入合同明细
    callIn() {
      this.$refs.contractList.Show();
    },
    async getContract(info = []) {
      let result2 = await cusAPI.checkContractAnexo({
        customer_contract_id: info[0].customer_contract_id,
      });

      if (!result2.data) {
        return this.error("该合同还未上传附件，请上传附件");
      }
      if (!this.ruleForm.customer_no) {
        //判断是否填写了财务信息
        let result1 = await cusAPI.getFinanceInfo({
          customer_id: info[0].fk_customer_id,
        });
        if (!result1.data) {
          return this.error("该客户未添加财务信息，不允许开票");
        }

        if (result1.data) {
          this.loading = true;
          this.ruleForm.open_ticket_address = result1.data.open_ticket_address;
          this.ruleForm.open_account_bank = result1.data.opening_bank;
          this.ruleForm.open_ticket_phone = result1.data.open_ticket_phone;
          this.ruleForm.receive_ticket_address =
            result1.data.receive_ticket_address;
          this.ruleForm.receive_ticket_person =
            result1.data.receive_ticket_person;
          this.ruleForm.receive_ticket_phone =
            result1.data.receive_ticket_phone;
          this.ruleForm.customer_tax_number = result1.data.customer_tax_number;
          this.ruleForm.customer_account = result1.data.customer_account;
          this.ruleForm.fk_customer_id = info[0].fk_customer_id;
          this.ruleForm.ticket_date = info[0].add_time;
          this.ruleForm.customer_name = info[0].customer_name;
          this.ruleForm.customer_no = info[0].customer_no;
          this.ruleForm.fk_sale_employee_name = info[0].fk_sell_employee_name;
          this.ruleForm.fk_sale_employee_id = info[0].fk_sell_employee_id;
          this.ruleForm.fk_sales_unit_id = info[0].sales_unit_id;
          this.ruleForm.sales_unit_id_format = info[0].sales_unit_id_format;
          info.forEach((key) => {
            this.fk_customer_contract_ids.push(key.customer_contract_id);
            conAPI
              .detailList({
                customer_contract_id: key.customer_contract_id,
              })
              .then((res) => {
                res.data.map((item) => {
                  if (item.detail_type == 1) {
                    let tax =
                      (item.contract_amount * item.invoice_tax_rate) / 100;
                    let pro = JSON.parse(JSON.stringify(this.obj));
                    pro.contract_id.value = key.customer_contract_id;
                    pro.contract_no.value = key.contract_no;
                    pro.brand_id.value = item.fk_brand_id;
                    pro.open_ticket_name.value = item.fk_brand_id;
                    pro.detail_sell_type.value = item.detail_sell_type;

                    pro.open_ticket_unit.value = item.contract_price;
                    pro.open_ticket_number.value = item.contract_count || 1;
                    pro.invoice_tax_rate.value = item.invoice_tax_rate;
                    pro.buckle_open_ticket_unit.value =
                      item.contract_amount - tax;
                    pro.tax.value = tax;
                    pro.open_ticket_money.value = (
                      item.contract_amount - tax
                    ).toFixed(2);
                    pro.leved_total.value = item.contract_amount;
                    // pro.software_version.value = item.software_version;
                    pro.measurement_unit.value = item.measurement_unit;
                    // pro.open_ticket_model.value = item.software_version;
                    pro.software_no.value = key.software_no;
                    this.$refs.tableCustom.add2(pro);
                  }
                });
              })
              .catch(() => {})
              .finally(() => {});
          });
          this.getTotalOpenMoney(info[0].fk_customer_id,info[0].sales_unit_id);
        }

        // 查询该客户的所有数据
      } else {
        info.forEach((key) => {
          this.fk_customer_contract_ids.push(key.customer_contract_id);
          conAPI
            .detailList({
              customer_contract_id: key.customer_contract_id,
            })
            .then((res) => {
              res.data.map((item) => {
                if (item.detail_type == 1) {
                  let tax =
                    (item.contract_amount * item.invoice_tax_rate) / 100;
                  let pro = JSON.parse(JSON.stringify(this.obj));
                  pro.contract_id.value = key.customer_contract_id;
                  pro.contract_no.value = key.contract_no;
                  pro.brand_id.value = item.fk_brand_id;
                  pro.open_ticket_name.value = item.fk_brand_id;
                  pro.detail_sell_type.value = item.detail_sell_type;
                  pro.open_ticket_unit.value = key.contract_amount;
                  pro.invoice_tax_rate.value = item.invoice_tax_rate;
                  pro.buckle_open_ticket_unit.value =
                    item.contract_amount - tax;
                  pro.tax.value = tax;
                  pro.open_ticket_money.value = item.contract_amount - tax;
                  pro.leved_total.value = item.contract_amount;
                  // pro.software_version.value = item.software_version;
                  pro.measurement_unit.value = item.measurement_unit;
                  // pro.open_ticket_model.value = item.software_version;
                  pro.software_no.value = key.software_no;

                  this.$refs.tableCustom.add2(pro);
                }
              });
            })
            .catch(() => {})
            .finally(() => {});
        });
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        ticket_no: "",
        ticket_date: "",
        ticket_remark: "",
        fk_sale_employee_id: "",
        fk_sale_employee_name: "",
        sell_type: "",
        customer_name: "",
        customer_no: "",
        customer_tax_number: "",
        customer_account: "",
        invoice_number: "",
        express_number: "",
        open_ticket_address: "",
        open_account_bank: "",
        open_ticket_phone: "",
        receive_ticket_address: "",
        receive_ticket_person: "",
        receive_ticket_phone: "",
        fk_sales_unit_id: "",
        sales_unit_id_format: "",
        update_user_name: "",
      };
      this.totalOpenMoney = 0
      this.$refs.tableCustom.empty();
      this.fk_customer_contract_ids = [];
    },
    getTotalMoney(money) {
      this.total_money = money;
    },
    getTotalOpenMoney(fk_customer_id,sales_unit_id) {
      API.getTotalOpenMoney({ fk_customer_id: fk_customer_id, sales_unit_id }).then((res) => {
        if(res.data){
          this.totalOpenMoney = res.data;
        }else{
          this.totalOpenMoney = 0
        }
        this.totalOpenMoney = this.totalOpenMoney.toFixed(2)

      }).finally(() => {
        this.loading = false
      })
    },

    // eslint-disable-next-line no-unused-vars
    delDetail(row) {},
  },
  computed: {
    ...mapGetters([
      "userInfo",
      "sell_type",
      "buttonPermissions",
      "software_version",
      "measurement_unit",
    ]),
    // 构造客户信息对象，用于邮件发送
    customerInfo() {
      return {
        customer_name: this.ruleForm.customer_name || '',
        customer_no: this.ruleForm.customer_no || '',
        email: this.ruleForm.customer_email || '', // 从开票单详情接口获取客户邮箱
        phone: this.ruleForm.receive_ticket_phone || '',
      };
    },
  },
};
