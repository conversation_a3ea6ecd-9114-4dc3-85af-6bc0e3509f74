import Axios from "@/api";
import environment from "@/api/environment";
import EmailAPI from "@/api/internalSystem/common/email.js";
import CustomerAPI from "@/api/internalSystem/customerManage/customerInfo/index.js";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import pdf from "vue-pdf";

export default {
  name: "InvoiceAttachmentBatch",
  components: {
    Pagination,
    pdf,
  },
  created() {
    // 确保默认值设置正确
    this.searchForm.send_status = "0";
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      tableData: [],
      selectedRows: [],
      searchForm: {
        customer_name: "",
        document_no: "",
        file_name: "",
        email: "",
        send_status: "0", // 发送状态：0-未发送，1-已发送，默认选择未发送
      },
      
      // 批量发送相关
      batchSending: false,
      progressDialogVisible: false,
      sendProgress: 0,
      sendStatus: "active", // active, success, exception
      totalSendCount: 0,
      sentCount: 0,
      successCount: 0,
      failCount: 0,
      currentSendingItem: null,

      // 预览相关
      showDoc: false,
      showPdf: false,
      showImages: false,
      url: "",
      anexoName: "",
      pageNum: 1,
      pageTotalNum: 1,
      loadedRatio: 0,
    };
  },
  methods: {
    Show() {
      this.dialogVisible = true;
      // 清空之前的选择状态
      this.selectedRows = [];
      // 确保发送状态默认为未发送
      this.searchForm.send_status = "0";
      this.resetSearch();
      this.$nextTick(() => {
        this.getList();
      });
    },

    // 获取发票附件列表
    getList(resetPage = false) {
      this.loading = true;

      // 创建searchForm的副本，避免修改原始数据
      let param = JSON.parse(JSON.stringify(this.searchForm));

      if (this.$refs.pagination) {
        Object.assign(param, this.$refs.pagination.obtain());
      } else {
        Object.assign(param, {
          pageNum: 1,
          pageSize: 20
        });
      }

      if (resetPage) {
        param.pageNum = 1;
      }

      // 处理发送状态参数
      console.log("查询参数 send_status:", param.send_status);
      if (param.send_status !== "" && param.send_status !== null && param.send_status !== undefined) {
        if (param.send_status === "0") {
          param.send_success_count = 0; // 未发送
        } else if (param.send_status === "1") {
          param.send_success_count = 1; // 已发送（大于0）
        }
        delete param.send_status; // 删除前端字段，使用后端需要的字段
      }
      console.log("最终查询参数:", param);

      // 调用新的接口
      Axios.post(`${environment.internalSystemAPI}invoice/queryInvoiceAttachments`, param)
        .then((res) => {
          this.tableData = res.data.map(item => ({
            ...item,
            sending: false // 添加单个发送状态
          }));

          if (this.$refs.pagination) {
            this.$refs.pagination.setTotal(res.totalCount);
          }

          // 清空之前的选择状态
          this.$nextTick(() => {
            if (this.$refs.xTable) {
              this.$refs.xTable.clearCheckboxRow();
            }
            this.selectedRows = [];
          });
        })
        .catch(() => {
          this.error("获取发票附件列表失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 重置搜索条件
    resetSearch() {
      this.searchForm = {
        customer_name: "",
        document_no: "",
        file_name: "",
        email: "",
        send_status: "0", // 重置时也默认选择未发送
      };
      this.getList(true);
    },

    // 处理选择变化
    handleSelectionChange() {
      this.selectedRows = this.$refs.xTable.getCheckboxRecords();
    },

    // 全选
    selectAll() {
      this.$refs.xTable.setAllCheckboxRow(true);
      this.handleSelectionChange();
    },

    // 清空选择
    clearSelection() {
      this.$refs.xTable.clearCheckboxRow();
      this.handleSelectionChange();
    },

    // 发送单个邮件
    async sendSingleEmail(row) {
      if (!row.email || !row.email.trim()) {
        this.error("请先填写邮箱地址");
        return;
      }

      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(row.email)) {
        this.error("请输入正确的邮箱地址");
        return;
      }

      row.sending = true;

      try {
        const emailData = {
          email: row.email,
          subject: `发票附件 - ${row.attachment_name}`,
          content: `您好，\n\n请查收发票附件：${row.attachment_name}。\n\n单据编号：${row.document_no}\n客户名称：${row.customer_name}\n\n谢谢！`,
          customer_id: row.customer_id, // 添加客户ID
          attachments: [{
            fileName: row.attachment_name,
            fileUrl: row.file_url
          }]
        };

        // 等待接口响应完成
        await this.sendEmailAPI(emailData);
        this.success("邮件发送成功");

        // 更新发送成功次数
        row.send_success_count += 1;

      } catch (error) {
        this.error("邮件发送失败：" + (error.message || "未知错误"));
      } finally {
        row.sending = false;
      }
    },

    // 批量发送邮件
    async batchSendEmails() {
      if (!this.selectedRows.length) {
        this.error("请先选择要发送的记录");
        return;
      }

      // 检查邮箱地址
      const invalidRows = this.selectedRows.filter(row => !row.email || !row.email.trim());
      if (invalidRows.length > 0) {
        this.error(`有 ${invalidRows.length} 条记录未填写邮箱地址，请先完善邮箱信息`);
        return;
      }

      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const invalidEmailRows = this.selectedRows.filter(row => !emailRegex.test(row.email));
      if (invalidEmailRows.length > 0) {
        this.error(`有 ${invalidEmailRows.length} 条记录邮箱格式不正确，请检查`);
        return;
      }

      // 确认发送
      try {
        await this.$confirm(`确定要发送 ${this.selectedRows.length} 封邮件吗？`, "确认发送", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
      } catch {
        return;
      }

      // 开始批量发送
      this.batchSending = true;
      this.progressDialogVisible = true;
      this.totalSendCount = this.selectedRows.length;
      this.sentCount = 0;
      this.successCount = 0;
      this.failCount = 0;
      this.sendProgress = 0;
      this.sendStatus = "active";

      for (let i = 0; i < this.selectedRows.length; i++) {
        const row = this.selectedRows[i];
        this.currentSendingItem = row;

        try {
          const emailData = {
            anexo_id: row.anexo_id,
            email: row.email,
            subject: `发票附件 - ${row.attachment_name}`,
            content: `您好，\n\n请查收发票附件：${row.attachment_name}。\n\n单据编号：${row.document_no}\n客户名称：${row.customer_name}\n\n谢谢！`,
            customer_id: row.customer_id, // 添加客户ID
            attachments: [{
              fileName: row.attachment_name,
              fileUrl: row.file_url
            }]
          };

          console.log(`开始发送邮件 (${i + 1}/${this.totalSendCount}):`, row.customer_name, row.email);

          // 等待接口响应完成后再继续
          await this.sendEmailAPI(emailData);
          this.successCount++;

          console.log(`邮件发送成功:`, row.customer_name);

          // 更新表格中的发送成功次数
          const tableRow = this.tableData.find(item => item.anexo_id === row.anexo_id);
          if (tableRow) {
            tableRow.send_success_count += 1;
          }

        } catch (error) {
          this.failCount++;
          console.error(`发送邮件失败 (${row.customer_name}):`, error);
        }

        this.sentCount++;
        this.sendProgress = Math.round((this.sentCount / this.totalSendCount) * 100);

        // 每发送完一个邮件后等待1秒，避免请求过于频繁
        if (i < this.selectedRows.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // 发送完成
      this.sendStatus = this.failCount === 0 ? "success" : "exception";
      this.currentSendingItem = null;
      this.batchSending = false;

      if (this.failCount === 0) {
        this.success(`批量发送完成！成功发送 ${this.successCount} 封邮件`);
      } else {
        this.warning(`批量发送完成！成功 ${this.successCount} 封，失败 ${this.failCount} 封`);
      }
    },

    // 预览文件
    previewFile(row) {
      if (!row.file_url) {
        this.error("文件链接不存在");
        return;
      }
      let fileType = row.file_suffix && row.file_suffix.toLowerCase();
      // 支持的文件类型
      const doc = ["docx", "doc", "xlsx", "xls", "txt", "ppt", "pptx"];
      const pdf = ["pdf"];
      const images = ["png", "jpg", "jpeg", "gif", "bmp", "webp"];
      const video = ["mp4", "avi", "mov", "wmv", "flv", "webm"];
      const audio = ["mp3", "wav", "ogg", "aac"];

      if (doc.includes(fileType)) {
        this.anexoName = row.attachment_name;
        this.url = encodeURIComponent(row.file_url);
        this.showDoc = true;
      } else if (pdf.includes(fileType)) {
        this.anexoName = row.attachment_name;
        this.openFullScreen();
        this.url = row.file_url;
        this.showPdf = true;
      } else if (images.includes(fileType)) {
        this.anexoName = row.attachment_name;
        this.url = row.file_url;
        this.showImages = true;
      } else if (video.includes(fileType) || audio.includes(fileType)) {
        // 对于视频和音频文件，直接在新窗口打开
        window.open(row.file_url, '_blank');
      } else {
        // 不支持预览的文件类型，提示用户下载查看
        this.$confirm(`${fileType || '该'} 格式文件不支持在线预览，是否下载查看？`, '提示', {
          confirmButtonText: '下载',
          cancelButtonText: '取消',
          type: 'info'
        }).then(() => {
          this.downloadFile(row);
        }).catch(() => {});
      }
    },

    // 下载文件
    downloadFile(row) {
      if (!row.file_url) {
        this.error("文件链接不存在");
        return;
      }

      // 创建一个临时的a标签来触发下载
      const link = document.createElement('a');
      link.href = row.file_url;
      link.download = row.attachment_name || '附件';
      link.target = '_blank';

      // 添加到DOM并触发点击
      document.body.appendChild(link);
      link.click();

      // 清理
      document.body.removeChild(link);

      this.success("开始下载文件");
    },

    // 发送邮件API调用
    async sendEmailAPI(emailData) {
      try {
        // 使用普通的邮件发送接口
        const result = await EmailAPI.sendEmail(emailData);

        // 发送成功后，更新客户邮箱信息
        if (emailData.customer_id && emailData.email) {
          try {
            await this.updateCustomerEmail(emailData.customer_id, emailData.email);
            console.log(`客户邮箱已更新: ${emailData.email}`);
          } catch (error) {
            console.warn('更新客户邮箱失败:', error);
            // 不影响邮件发送的成功状态
          }
        }

        return result;
      } catch (error) {
        throw error;
      }
    },

    // 更新客户邮箱信息
    async updateCustomerEmail(customerId, email) {
      const params = {
        customer_id: customerId,
        email: email
      };
      return CustomerAPI.update(params);
    },

    // 预览相关方法
    openFullScreen() {
      let loading = this.$loading({
        lock: true,
        text: '正在加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      return loading;
    },

    closeFullScreen(loading) {
      loading.close();
    },

    closePreviewClick() {
      this.showDoc = false;
      this.showPdf = false;
      this.showImages = false;
      this.pageNum = 1;
      this.pageTotalNum = 1;
    },

    // 改变PDF页码
    prePage() {
      var page = this.pageNum;
      page = page > 1 ? page - 1 : 1;
      this.pageNum = page;
    },

    nextPage() {
      var page = this.pageNum;
      page = page < this.pageTotalNum ? page + 1 : this.pageTotalNum;
      this.pageNum = page;
    },

    dialogCancel() {
      this.dialogVisible = false;
      this.selectedRows = [];
      // 清空表格选择状态
      if (this.$refs.xTable) {
        this.$refs.xTable.clearCheckboxRow();
      }
    },

    // 消息提示方法
    success(message) {
      this.$message({
        type: "success",
        message: message,
      });
    },

    error(message) {
      this.$message({
        type: "error",
        message: message,
      });
    },

    warning(message) {
      this.$message({
        type: "warning",
        message: message,
      });
    },
  },
};
