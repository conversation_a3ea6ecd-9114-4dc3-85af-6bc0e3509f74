import Axios from "@/api";
import environment from "@/api/environment";
import EmailAPI from "@/api/internalSystem/common/email.js";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";

export default {
  name: "InvoiceAttachmentBatch",
  components: {
    Pagination,
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      tableData: [],
      selectedRows: [],
      searchForm: {
        customer_name: "",
        document_no: "",
        file_name: "",
        email: "",
        send_success_count: null,
      },
      
      // 批量发送相关
      batchSending: false,
      progressDialogVisible: false,
      sendProgress: 0,
      sendStatus: "active", // active, success, exception
      totalSendCount: 0,
      sentCount: 0,
      successCount: 0,
      failCount: 0,
      currentSendingItem: null,
    };
  },
  methods: {
    Show() {
      this.dialogVisible = true;
      this.resetSearch();
      this.$nextTick(() => {
        this.getList();
      });
    },

    // 获取发票附件列表
    getList(resetPage = false) {
      this.loading = true;
      
      let param = {};
      if (this.$refs.pagination) {
        param = Object.assign(this.searchForm, this.$refs.pagination.obtain());
      } else {
        param = Object.assign(this.searchForm, {
          pageNum: 1,
          pageSize: 20
        });
      }
      
      if (resetPage) {
        param.pageNum = 1;
      }

      // 调用新的接口
      Axios.post(`${environment.internalSystemAPI}invoice/queryInvoiceAttachments`, param)
        .then((res) => {
          this.tableData = res.data.map(item => ({
            ...item,
            sending: false // 添加单个发送状态
          }));
          
          if (this.$refs.pagination) {
            this.$refs.pagination.setTotal(res.totalCount);
          }
        })
        .catch(() => {
          this.error("获取发票附件列表失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 重置搜索条件
    resetSearch() {
      this.searchForm = {
        customer_name: "",
        document_no: "",
        file_name: "",
        email: "",
        send_success_count: null,
      };
      this.getList(true);
    },

    // 处理选择变化
    handleSelectionChange() {
      this.selectedRows = this.$refs.xTable.getCheckboxRecords();
    },

    // 全选
    selectAll() {
      this.$refs.xTable.setAllCheckboxRow(true);
      this.handleSelectionChange();
    },

    // 清空选择
    clearSelection() {
      this.$refs.xTable.clearCheckboxRow();
      this.handleSelectionChange();
    },

    // 发送单个邮件
    async sendSingleEmail(row) {
      if (!row.email || !row.email.trim()) {
        this.error("请先填写邮箱地址");
        return;
      }

      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(row.email)) {
        this.error("请输入正确的邮箱地址");
        return;
      }

      row.sending = true;

      try {
        const emailData = {
          email: row.email,
          subject: `发票附件 - ${row.attachment_name}`,
          content: `您好，\n\n请查收发票附件：${row.attachment_name}。\n\n单据编号：${row.document_no}\n客户名称：${row.customer_name}\n\n谢谢！`,
          attachments: [{
            fileName: row.attachment_name,
            fileUrl: row.file_url
          }]
        };

        await this.sendEmailAPI(emailData);
        this.success("邮件发送成功");
        
        // 更新发送成功次数
        row.send_success_count += 1;
        
      } catch (error) {
        this.error("邮件发送失败：" + (error.message || "未知错误"));
      } finally {
        row.sending = false;
      }
    },

    // 批量发送邮件
    async batchSendEmails() {
      if (!this.selectedRows.length) {
        this.error("请先选择要发送的记录");
        return;
      }

      // 检查邮箱地址
      const invalidRows = this.selectedRows.filter(row => !row.email || !row.email.trim());
      if (invalidRows.length > 0) {
        this.error(`有 ${invalidRows.length} 条记录未填写邮箱地址，请先完善邮箱信息`);
        return;
      }

      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const invalidEmailRows = this.selectedRows.filter(row => !emailRegex.test(row.email));
      if (invalidEmailRows.length > 0) {
        this.error(`有 ${invalidEmailRows.length} 条记录邮箱格式不正确，请检查`);
        return;
      }

      // 确认发送
      try {
        await this.$confirm(`确定要发送 ${this.selectedRows.length} 封邮件吗？`, "确认发送", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
      } catch {
        return;
      }

      // 开始批量发送
      this.batchSending = true;
      this.progressDialogVisible = true;
      this.totalSendCount = this.selectedRows.length;
      this.sentCount = 0;
      this.successCount = 0;
      this.failCount = 0;
      this.sendProgress = 0;
      this.sendStatus = "active";

      for (let i = 0; i < this.selectedRows.length; i++) {
        const row = this.selectedRows[i];
        this.currentSendingItem = row;

        try {
          const emailData = {
            email: row.email,
            subject: `发票附件 - ${row.attachment_name}`,
            content: `您好，\n\n请查收发票附件：${row.attachment_name}。\n\n单据编号：${row.document_no}\n客户名称：${row.customer_name}\n\n谢谢！`,
            attachments: [{
              fileName: row.attachment_name,
              fileUrl: row.file_url
            }]
          };

          await this.sendEmailAPI(emailData);
          this.successCount++;
          
          // 更新表格中的发送成功次数
          const tableRow = this.tableData.find(item => item.anexo_id === row.anexo_id);
          if (tableRow) {
            tableRow.send_success_count += 1;
          }
          
        } catch (error) {
          this.failCount++;
          console.error(`发送邮件失败 (${row.customer_name}):`, error);
        }

        this.sentCount++;
        this.sendProgress = Math.round((this.sentCount / this.totalSendCount) * 100);

        // 添加延迟避免请求过于频繁
        if (i < this.selectedRows.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // 发送完成
      this.sendStatus = this.failCount === 0 ? "success" : "exception";
      this.currentSendingItem = null;
      this.batchSending = false;

      if (this.failCount === 0) {
        this.success(`批量发送完成！成功发送 ${this.successCount} 封邮件`);
      } else {
        this.warning(`批量发送完成！成功 ${this.successCount} 封，失败 ${this.failCount} 封`);
      }
    },

    // 发送邮件API调用
    async sendEmailAPI(emailData) {
      // 使用带附件的邮件发送接口
      return EmailAPI.sendEmailWithAttachments(emailData);
    },

    dialogCancel() {
      this.dialogVisible = false;
      this.selectedRows = [];
      this.clearSelection();
    },

    // 消息提示方法
    success(message) {
      this.$message({
        type: "success",
        message: message,
      });
    },

    error(message) {
      this.$message({
        type: "error",
        message: message,
      });
    },

    warning(message) {
      this.$message({
        type: "warning",
        message: message,
      });
    },
  },
};
