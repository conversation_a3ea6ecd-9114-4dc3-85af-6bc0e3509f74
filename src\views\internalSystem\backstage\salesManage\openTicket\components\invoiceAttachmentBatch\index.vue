<template>
  <el-dialog
    title="发票附件批量发送"
    :visible.sync="dialogVisible"
    width="80%"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    append-to-body
    v-dialogDrag
  >
    <!-- 筛选条件 -->
    <el-form :inline="true" :model="searchForm" size="small" class="mb20">
      <el-form-item label="客户名称">
        <el-input
          v-model="searchForm.customer_name"
          placeholder="请输入客户名称"
          clearable
          style="width: 160px"
        ></el-input>
      </el-form-item>
      <el-form-item label="单据编号">
        <el-input
          v-model="searchForm.document_no"
          placeholder="请输入单据编号"
          clearable
          style="width: 160px"
        ></el-input>
      </el-form-item>
      <el-form-item label="附件名称">
        <el-input
          v-model="searchForm.file_name"
          placeholder="请输入附件名称"
          clearable
          style="width: 160px"
        ></el-input>
      </el-form-item>
      <el-form-item label="邮箱地址">
        <el-input
          v-model="searchForm.email"
          placeholder="请输入邮箱地址"
          clearable
          style="width: 160px"
        ></el-input>
      </el-form-item>
      <el-form-item label="发送成功次数">
        <el-input-number
          v-model="searchForm.send_success_count"
          :min="0"
          placeholder="发送成功次数"
          style="width: 120px"
        ></el-input-number>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading">
          查询
        </el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="mb20">
      <el-button
        type="primary"
        @click="batchSendEmails"
        :loading="batchSending"
        :disabled="!selectedRows.length"
      >
        批量发送邮件 ({{ selectedRows.length }})
      </el-button>
      <el-button @click="selectAll">全选</el-button>
      <el-button @click="clearSelection">清空选择</el-button>
    </div>

    <!-- 数据表格 -->
    <vxe-table
      ref="xTable"
      border
      resizable
      highlight-hover-row
      auto-resize
      :height="500"
      :loading="loading"
      :data="tableData"
      :checkbox-config="{ reserve: true, showHeader: true }"
      @checkbox-change="handleSelectionChange"
      @checkbox-all="handleSelectionChange"
      size="small"
      row-id="anexo_id"
    >
      <vxe-table-column type="checkbox" width="50" align="center"></vxe-table-column>
      <vxe-table-column type="seq" title="序号" width="60" align="center"></vxe-table-column>
      
      <vxe-table-column field="customer_name" title="客户名称" width="200">
        <template v-slot="{ row }">
          <span>{{ row.customer_name }}</span>
        </template>
      </vxe-table-column>
      
      <vxe-table-column field="document_no" title="单据编号" width="150">
        <template v-slot="{ row }">
          <span>{{ row.document_no }}</span>
        </template>
      </vxe-table-column>
      
      <vxe-table-column field="document_type" title="单据类型" width="100">
        <template v-slot="{ row }">
          <el-tag :type="row.document_type === 'contract' ? 'success' : 'primary'">
            {{ row.document_type === 'contract' ? '合同' : '开票单' }}
          </el-tag>
        </template>
      </vxe-table-column>
      
      <vxe-table-column field="attachment_name" title="附件名称" min-width="200">
        <template v-slot="{ row }">
          <span>{{ row.attachment_name }}</span>
        </template>
      </vxe-table-column>
      
      <vxe-table-column field="email" title="邮箱地址" width="200" :edit-render="{ name: 'input' }">
        <template v-slot:edit="{ row }">
          <el-input v-model="row.email" placeholder="请输入邮箱地址"></el-input>
        </template>
      </vxe-table-column>
      
      <vxe-table-column field="send_success_count" title="发送成功次数" width="120" align="center">
        <template v-slot="{ row }">
          <el-tag :type="row.send_success_count > 0 ? 'success' : 'info'">
            {{ row.send_success_count }}
          </el-tag>
        </template>
      </vxe-table-column>
      
      <vxe-table-column title="操作" width="120" fixed="right" align="center">
        <template v-slot="{ row }">
          <el-button
            type="text"
            size="small"
            @click="sendSingleEmail(row)"
            :loading="row.sending"
          >
            发送邮件
          </el-button>
        </template>
      </vxe-table-column>
    </vxe-table>

    <!-- 分页 -->
    <Pagination ref="pagination" @success="getList" class="mt20" />

    <!-- 批量发送进度弹窗 -->
    <el-dialog
      title="批量发送进度"
      :visible.sync="progressDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      :show-close="false"
      append-to-body
    >
      <div>
        <el-progress
          :percentage="sendProgress"
          :status="sendStatus"
          :stroke-width="20"
        ></el-progress>
        <div class="mt10">
          <p>总数：{{ totalSendCount }}</p>
          <p>已发送：{{ sentCount }}</p>
          <p>成功：{{ successCount }}</p>
          <p>失败：{{ failCount }}</p>
        </div>
        <div class="mt10" v-if="currentSendingItem">
          <p>正在发送：{{ currentSendingItem.customer_name }} - {{ currentSendingItem.attachment_name }}</p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer" v-if="sendStatus !== 'active'">
        <el-button @click="progressDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogCancel">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
.mb20 {
  margin-bottom: 20px;
}
.mt10 {
  margin-top: 10px;
}
.mt20 {
  margin-top: 20px;
}

// 表格样式优化
::v-deep .vxe-table {
  .vxe-body--row.row--hover {
    background-color: #f5f7fa;
  }

  .vxe-cell {
    padding: 8px 4px;
  }

  .el-tag {
    margin: 0;
  }
}

// 进度弹窗样式
::v-deep .el-progress-bar__outer {
  background-color: #f0f2f5;
}

::v-deep .el-progress-bar__inner {
  transition: width 0.3s ease;
}
</style>
